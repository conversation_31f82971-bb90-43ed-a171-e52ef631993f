[project]
name = "pr-taxonomy-apis"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastapi>=0.117.1",
    "google-genai>=1.38.0",
    "langchain[all]>=0.3.27",
    "langextract>=1.0.9",
    "numpy>=2.3.3",
    "openai>=1.109.1",
    "openpyxl>=3.1.5",
    "pandas>=2.3.2",
    "python-dotenv>=1.1.1",
    "python-multipart>=0.0.20",
    "requests>=2.32.5",
    "uvicorn[standard]>=0.37.0",
]
