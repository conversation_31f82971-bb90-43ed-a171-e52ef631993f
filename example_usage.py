#!/usr/bin/env python3
"""
Example Usage of LangExtract Patent Taxonomy Extraction
This script shows how to use the langextract integration for patent analysis.
"""

import os
import sys
from typing import List

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from patent_langextract import (
    PatentLangExtractProcessor,
    extract_taxonomies_from_patent,
    extract_taxonomies_from_text
)
from config import Config

def example_1_single_patent():
    """Example 1: Extract taxonomies from a single patent"""
    print("=" * 60)
    print("Example 1: Single Patent Extraction")
    print("=" * 60)
    
    # Define the taxonomies you want to extract
    taxonomies = [
        "Genome Recoding",
        "Synthetic organism/ genomically recoded organisms (GRO)",
        "Non-canonical amino acids (ncAA)",
        "Orthogonal translation system (OTS)"
    ]
    
    # Use the convenience function
    results = extract_taxonomies_from_patent(
        patent_number="US11408007B2",
        taxonomies=taxonomies,
        model_id="gemini-2.5-flash"  # or "gpt-4o"
    )
    
    print(f"Patent: {results['patent_number']}")
    print("\nExtracted Taxonomies:")
    
    for taxonomy in taxonomies:
        key = taxonomy.lower().replace(' ', '_').replace('/', '_')
        text_key = f"{key}_text"
        confidence_key = f"{key}_confidence"
        count_key = f"{key}_count"
        
        if text_key in results and results[text_key]:
            print(f"\n📋 {taxonomy}:")
            print(f"   Text: {results[text_key][:200]}...")
            print(f"   Confidence: {results[confidence_key]:.2f}")
            print(f"   Chunks found: {results[count_key]}")
        else:
            print(f"\n📋 {taxonomy}: No relevant text found")

def example_2_batch_processing():
    """Example 2: Process multiple patents in batch"""
    print("\n" + "=" * 60)
    print("Example 2: Batch Processing")
    print("=" * 60)
    
    # Initialize the processor
    processor = PatentLangExtractProcessor(
        model_id="gemini-2.5-flash",
        chunk_size=10000  # Smaller chunks for faster processing
    )
    
    # Define patents and taxonomies
    patent_numbers = ["US11408007B2", "US11649450B2", "US11788111B2"]
    taxonomies = [
        "Genome Recoding",
        "Non-canonical amino acids (ncAA)"
    ]
    
    print(f"Processing {len(patent_numbers)} patents for {len(taxonomies)} taxonomies...")
    
    # Process in batch
    all_results = processor.extract_patent_taxonomies_batch(
        patent_numbers=patent_numbers,
        taxonomies=taxonomies,
        min_confidence=0.7  # Higher confidence threshold
    )
    
    # Display results
    for patent_number, patent_results in all_results.items():
        print(f"\n📄 Patent: {patent_number}")
        for taxonomy, result in patent_results.items():
            chunks_found = len(result.extracted_chunks)
            print(f"  - {taxonomy}: {chunks_found} relevant chunks")
            if chunks_found > 0:
                avg_confidence = sum(result.confidence_scores) / len(result.confidence_scores)
                print(f"    Average confidence: {avg_confidence:.2f}")
    
    # Export to CSV
    df = processor.export_results_to_dataframe(all_results)
    
    # Create output directory
    os.makedirs(Config.OUTPUT_DIR, exist_ok=True)
    output_path = os.path.join(Config.OUTPUT_DIR, "batch_extraction_results.csv")
    df.to_csv(output_path, index=False)
    
    print(f"\n💾 Results exported to: {output_path}")
    print(f"   Total rows: {len(df)}")

def example_3_custom_text():
    """Example 3: Extract from custom abstract and claims text"""
    print("\n" + "=" * 60)
    print("Example 3: Custom Text Extraction")
    print("=" * 60)
    
    # Sample patent text (you can replace with your own)
    abstract = """
    The present invention provides methods for engineering bacterial cells with modified 
    genetic codes. Specifically, the invention involves reassigning amber stop codons 
    (UAG) to encode non-canonical amino acids, creating genomically recoded organisms 
    (GROs) with enhanced biosynthetic capabilities. The method includes introducing 
    orthogonal translation systems that enable incorporation of synthetic amino acids 
    into proteins during translation.
    """
    
    claims = """
    1. A method for genome recoding comprising:
       (a) modifying the genetic code of a bacterial cell;
       (b) reassigning at least one stop codon to encode a non-canonical amino acid;
       (c) introducing an orthogonal translation system;
       (d) selecting cells with successful genome recoding.
    
    2. The method of claim 1, wherein the non-canonical amino acid is incorporated 
       into proteins during translation.
    
    3. The method of claim 1, wherein the genomically recoded organism exhibits 
       enhanced biosynthetic properties.
    """
    
    taxonomies = [
        "Genome Recoding",
        "Non-canonical amino acids (ncAA)",
        "Orthogonal translation system (OTS)",
        "Synthetic organism/ genomically recoded organisms (GRO)"
    ]
    
    # Extract taxonomies from the text
    results = extract_taxonomies_from_text(
        abstract=abstract,
        claims=claims,
        taxonomies=taxonomies,
        patent_number="CUSTOM_EXAMPLE"
    )
    
    print("Custom Text Analysis Results:")
    print(f"Patent ID: {results['patent_number']}")
    
    for taxonomy in taxonomies:
        key = taxonomy.lower().replace(' ', '_').replace('/', '_')
        text_key = f"{key}_text"
        confidence_key = f"{key}_confidence"
        
        if text_key in results and results[text_key]:
            print(f"\n🔍 {taxonomy}:")
            print(f"   Found: {results[text_key]}")
            print(f"   Confidence: {results[confidence_key]:.2f}")

def example_4_generate_reports():
    """Example 4: Generate HTML visualization reports"""
    print("\n" + "=" * 60)
    print("Example 4: Generate Visualization Reports")
    print("=" * 60)
    
    processor = PatentLangExtractProcessor()
    
    patent_number = "US11408007B2"
    taxonomies = ["Genome Recoding", "Non-canonical amino acids (ncAA)"]
    
    # Process the patent
    results = processor.process_patent_with_taxonomies(
        patent_number=patent_number,
        taxonomies=taxonomies
    )
    
    # Fetch patent data for the report
    patent_data = processor.fetch_patent_data(patent_number)
    
    # Generate HTML report
    os.makedirs(Config.REPORTS_DIR, exist_ok=True)
    report_path = os.path.join(Config.REPORTS_DIR, f"report_{patent_number}.html")
    
    processor.generate_visualization_report(
        results=results,
        patent_data=patent_data,
        output_path=report_path
    )
    
    print(f"📊 HTML report generated: {report_path}")
    print("   Open this file in a web browser to view the interactive visualization")

def main():
    """Run all examples"""
    print("🚀 LangExtract Patent Taxonomy Extraction Examples")
    print(f"Configuration:")
    print(f"  - Gemini API: {'✅ Configured' if Config.GEMINI_API_KEY else '❌ Missing'}")
    print(f"  - OpenAI API: {'✅ Configured' if Config.OPENAI_API_KEY else '❌ Missing'}")
    print(f"  - Patent API: {Config.PATENT_API_BASE_URL}")
    
    try:
        example_1_single_patent()
        example_2_batch_processing()
        example_3_custom_text()
        example_4_generate_reports()
        
        print("\n" + "=" * 60)
        print("🎉 All examples completed successfully!")
        print("=" * 60)
        print("\nNext steps:")
        print("1. Check the generated CSV files in the 'results' directory")
        print("2. Open the HTML reports in the 'reports' directory")
        print("3. Modify the taxonomies and patent numbers for your use case")
        
    except Exception as e:
        print(f"\n❌ Error running examples: {str(e)}")
        print("Please check your API keys and internet connection.")

if __name__ == "__main__":
    main()
