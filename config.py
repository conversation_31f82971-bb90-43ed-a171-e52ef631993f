import os
from dotenv import load_dotenv

load_dotenv()


class Config:
    GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    PATENT_API_BASE_URL = os.getenv('PATENT_API_BASE_URL', 'https://api.patent.wissenresearch.com')

    # LangExtract Configuration
    DEFAULT_CHUNK_SIZE = 4000
    DEFAULT_OVERLAP = 200
    MIN_CONFIDENCE_THRESHOLD = 0.6

    # Output Configuration
    OUTPUT_DIR = "results"
    REPORTS_DIR = "reports"