import os
from dotenv import load_dotenv

load_dotenv()


class Config:
    # API Keys - support multiple environment variable names for compatibility
    GEMINI_API_KEY = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY') or os.getenv('openai_key')

    # Patent API Configuration
    PATENT_API_BASE_URL = os.getenv('PATENT_API_BASE_URL') or os.getenv('patent_data_url', 'https://api.patent.wissenresearch.com').rstrip('/')

    # Model Configuration
    DEFAULT_GEMINI_MODEL = os.getenv('MODEL_ID', 'gemini-2.5-flash')
    DEFAULT_OPENAI_MODEL = os.getenv('MODEL_NAME', 'gpt-4o')

    # LangExtract Configuration
    DEFAULT_CHUNK_SIZE = int(os.getenv('LANGEXTRACT_CHUNK_SIZE', '4000'))
    DEFAULT_OVERLAP = int(os.getenv('LANGEXTRACT_OVERLAP', '200'))
    MIN_CONFIDENCE_THRESHOLD = float(os.getenv('LANGEXTRACT_MIN_CONFIDENCE', '0.6'))

    # Processing Configuration
    MAX_WORKERS = int(os.getenv('LANGEXTRACT_MAX_WORKERS', '4'))
    EXTRACTION_PASSES = int(os.getenv('LANGEXTRACT_PASSES', '2'))
    REQUEST_TIMEOUT = int(os.getenv('REQUEST_TIMEOUT', '30'))

    # Output Configuration
    OUTPUT_DIR = os.getenv('OUTPUT_DIR', 'results')
    REPORTS_DIR = os.getenv('REPORTS_DIR', 'reports')

    # Logging Configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')