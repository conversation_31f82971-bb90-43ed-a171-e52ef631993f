#%% md
#### Install the required libraries
#%%
%pip install langextract
#%% md
#### Import the libraries and set up the dotenv file with LANGEXTRACT_API_KEY, which uses the GEMINI API
#%%
import textwrap
import langextract as lx
from dotenv import load_dotenv
import os

load_dotenv()
#%% md
#### Define the prompt with the extraction requirements
#%%
prompt = textwrap.dedent("""\
Extract the company name, specific financial metrics, and market sentiment from the text.
    Use exact text for extractions. Do not paraphrase or overlap entities.
    Provide meaningful attributes for each entity to add context.
    - For companies, include the stock ticker.
    - For financial metrics, specify the type and value.
    - For market sentiment, classify it as 'bullish', 'bearish', or 'neutral'.""")
#%% md
#### Provide a high-quality example to guide the model
#%%
examples = [
    lx.data.ExampleData(
        text=(
            "AlphaTech (AT) announced a quarterly profit of $2.5 billion, exceeding analyst expectations"
            " and signaling a strongly bullish trend for the sector."
        ),
        extractions=[
            lx.data.Extraction(
                extraction_class="company",
                extraction_text="AlphaTech",
                attributes={"stock_ticker": "AT"},
            ),
            lx.data.Extraction(
                extraction_class="financial_metric",
                extraction_text="quarterly profit of $2.5 billion",
                attributes={"metric_type": "profit", "value": "$2.5 billion"}
            ),
            lx.data.Extraction(
                extraction_class="market_sentiment",
                extraction_text="strongly bullish trend",
                attributes={"sentiment": "bullish"}
            ),
        ],
    )
]
#%% md
#### Provide an input for processing and execute the extraction process 
#%%
input_text = (
    "Global Dynamics Inc. (GDI) reported a staggering quarterly revenue of $15 billion, \
    but its stock dipped 2%, leading to a neutral but cautious market outlook."
)
result = lx.extract(
    text_or_documents=input_text,
    prompt_description=prompt,
    examples=examples,
    model_id="gemini-2.5-pro",
)
#%% md
#### Save the results in a JSONL file
#%%
lx.io.save_annotated_documents([result], output_name="extraction_results.jsonl")
#%% md
#### Visualise the results 
#%%
html_content = lx.visualize("extraction_results.jsonl")
html_content
#%%

#%%
