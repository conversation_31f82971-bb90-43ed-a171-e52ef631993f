
import pandas as pd 
import os 

from extraction_methods import *

abstract =  "Disclosed is a method for manufacturing an electrode for a lithium secondary battery, including the steps of: (S1) dry mixing a conductive material and an electrode active material; (S2) dry mixing the resultant product of step (S1) with a binder to obtain electrode mixture powder; and (S3) applying the electrode mixture powder to at least one surface of a current collector. According to an embodiment of the present disclosure, electrode mixture powder is prepared without using a solvent and is applied to a current collector to provide an electrode. Thus, there is no need for a separate drying step. Therefore, the binder and conductive material coated on the surface of electrode active material cause no surface migration phenomenon. As a result, it is possible to prevent degradation of the adhesion of the electrode, and thus to prevent degradation of the performance of the lithium secondary battery."
claims = """What is claimed is:
1. A method for manufacturing an electrode for a lithium secondary battery, comprising the steps of: (S1) dry mixing and agitating a conductive material and an electrode active material at a rotation speed of 1,000-2,000 rpm; (S2) dry mixing and agitating the resultant product of step (S1) with a binder, at a rotation speed of 1,000-2,000 rpm, to obtain a dry electrode mixture powder; and (S3) applying the dry electrode mixture powder to at least one surface of a current collector,
 wherein (S1) and (S2) are each carried out through a mechano-chemical reaction.
2. The method for manufacturing an electrode for a lithium secondary battery according to claim 1, which farther comprises, after step (S3), a step of allowing the resultant product of step (S3) to pass through hot press rolls.
3. The method for manufacturing an electrode for a lithium secondary battery according to claim 1, wherein the electrode for a lithium secondary battery is a positive electrode, and in step (S3), the dry electrode mixture powder is applied to at least one surface of the current collector in a loading amount of 25-50 mg/cm2.
4. The method for manufacturing an electrode for a lithium secondary battery according to claim 1, the electrode for a lithium secondary battery is a negative electrode, and in step (S3), the dry electrode mixture powder is applied to at least one surface of the current collector in a loading amount of 12-40 mg/cm2.
5. The method for manufacturing an electrode for a lithium secondary battery according to claim 1, wherein the conductive material is any one selected from the group consisting of acetylene black, carbon black, ketjen black, graphite and a mixture thereof, or a combination of two or more of them.
6. The method for manufacturing an electrode for a lithium secondary battery according to claim 1, wherein the electrode active material is positive electrode active material particles or negative electrode active material particles.
7. The method for manufacturing an electrode for a lithium secondary battery according to claim 6, wherein the electrode active material is positive electrode active material particles that are at least one selected from the group consisting of LiCoO2, LiNiO2, LiMn2O4, LiCoPO4, LiFePO4, LiNi1/3Mn1/3CO1/3O2 and LiNi1-x-y-zCoxM1yM2zO2 (wherein each of M1 and M2 independently represents any one of Al, Ni, Co, Fe, Mn, V, Cr, Ti, W, Ta, Mg and Mo, each of x, y and z independently represents an atomic proportion of an element forming the oxide, 0≤x<0.5, 0≤y<0.5, 0≤z<0.5, and 0<x+y+z≤1).
8. The method for manufacturing an electrode for a lithium secondary battery according to claim 6, wherein the electrode active material is negative electrode active material particles that are at least one selected from the group consisting of: carbonaceous materials; lithium-containing titanium composite oxide (LTO) and metals (Me); alloys of the metals (Me); oxides (MeOx) of the metals (Me); and composites of the metals (Me) with carbon.
9. The method for manufacturing an electrode for a lithium secondary battery according to claim 1, wherein the binder is any one selected from the group consisting of polyvinylidene fluoride, polyvinylidene fluoride-co-hexafluoropropylene, polyvinylidene fluoride-co-trichloroethylene, polymethyl methacrylate, polyacrylonitrile, polyvinyl pyrrolidone, polyvinyl acetate, polyethylene-co-vinyl acetate, polyethylene oxide, cellulose acetate, cellulose acetate butyrate, cellulose acetate priopionate, cyanoethyl pullulan, cyanoethyl polyvinyl alcohol, cyanoethyl cellulose, cyanoethyl sucrose, pullulan and carboxymethyl cellulose, or a combination of two or more of them.
10. The method for manufacturing an electrode for a lithium secondary battery according to claim 1, wherein the current collector comprises any one of stainless steel; aluminum; nickel; titanium; copper; stainless steel surface treated with carbon, nickel, titanium or silver; and aluminum-cadmium alloy, or a combination of two or more of them.
11. The method for manufacturing an electrode for a lithium secondary battery according to claim 8, wherein the negative electrode active material particles are carbonaceous materials selected from the group consisting of natural graphite and artificial graphite.
12. The method for manufacturing an electrode for a lithium secondary battery according to claim 8, wherein the negative electrode active material particles are metals (Me) selected from the group consisting of Si, Sn, Li, Zn, Mg, Cd, Ce, Ni and Fe.
"""


def extract_all(sno, patent_number, abstract, claims, csv_filename='extracted_data.csv'):
    """
    Extracts all relevant information from the abstract and claims.
    """
    response_json_active = extract_active_materials(abstract, claims)
    reasponse_json_additives = extract_additives(abstract, claims)
    response_json_binder = extract_binders(abstract, claims)
    response_electrolyte = exrtract_electrolyte(abstract, claims)
    response_current_collector = extract_current_collector(abstract, claims)
    response_mixing_mass_ratio = extract_mixing_mass_ratio(abstract, claims)
    response_mixing = extract_mixing(abstract, claims)
    response_mixing_process_parameters = extract_mixing_process_parameters(abstract, claims)
    response_process_film_production = extract_process_film_production(abstract, claims)
    response_film_properties = extract_film_properties(abstract, claims)
    response_lamination = extract_lamination(abstract, claims)
    response_electrode_structural_pattern = extract_electrode_structural_pattern(abstract, claims)
    response_properties = extract_properties(abstract, claims)
    respone_application = extract_application(abstract, claims)

    abstract_data = {"abstract": abstract}
    claims_data = {"claims": claims}
    sno = {"S.No": sno}
    patent_number = {"Patent Number": patent_number}

    merged_data = {
        **sno,
        **patent_number,
        **abstract_data,
        **claims_data,
        **response_json_active,
        **reasponse_json_additives,
        **response_json_binder,
        **response_electrolyte,
        **response_current_collector,
        **response_mixing_mass_ratio,
        **response_mixing,
        **response_mixing_process_parameters,
        **response_process_film_production,
        **response_film_properties,
        **response_lamination,
        **response_electrode_structural_pattern,
        **response_properties,
        **respone_application
        
    }

    df = pd.DataFrame([merged_data])
    if os.path.exists(csv_filename):
        df.to_csv(csv_filename, mode='a', header=False, index=False)
    else:
        df.to_csv(csv_filename, index=False)


# main

# extract_all(abstract, claims, csv_filename='extracted_data.csv')

ROW_NUMBER_START = 8 
ROW_NUMBER_END = 501
col_number_sno = 1
col_number_patent_number = 2
col_number_abstract = 83
col_number_claims = 84

file_name = "WR_file.xlsx"

df = pd.read_excel(file_name, sheet_name="Relevant Results")

for start in range(ROW_NUMBER_START,ROW_NUMBER_END+1):
    sno = df.iloc[start, col_number_sno]
    patent_number = df.iloc[start, col_number_patent_number]
    abstract = df.iloc[start, col_number_abstract]
    claims = df.iloc[start, col_number_claims]

    extract_all(sno,patent_number,abstract, claims, csv_filename='Results/extracted_data_all.csv')
    print(f"Extracted data for row {start} with abstract and claims.")
