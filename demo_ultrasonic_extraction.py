#!/usr/bin/env python3
"""
Demo: Ultrasonic Sensor Taxonomy Extraction with Mock Data
This demonstrates how the system works without requiring API calls.
"""

import os
import sys
import pandas as pd
from typing import List, Dict, Any
from dataclasses import dataclass

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

@dataclass
class MockTaxonomyResult:
    """Mock result for demonstration"""
    taxonomy: str
    extracted_chunks: List[str]
    confidence_scores: List[float]
    source_spans: List[tuple]
    context: str

def create_mock_ultrasonic_data():
    """Create mock patent data based on your ultrasonic sensor examples"""
    
    # Mock patent data from your examples
    patents = {
        "US20250189647A1": {
            "title": "Method for ultrasound-based object classification and device for performing ultrasound-based object classification",
            "abstract": """Conventional ultrasonic sensors lacks with accurate object classification and dimension determination, especially in complex driving scenarios. The invention introduces a method and device for ultrasound-based object classification using multiple ultrasonic sensors to improve classification and domain adaptation.""",
            "claims": """1. A method for ultrasound-based object classification comprising: using ultrasonic time signals of at least one sensor taken as an analog signal directly at the output of the electrical amplification circuit after the piezo element. 2. The method according to claim 1, wherein the classifier device includes a neural network or a convolutional neural network (CNN), with feature maps of the first ultrasonic sensor and/or of the adjacent ultrasonic sensors.""",
            "description": """According to an example embodiment of the present invention, ultrasonic time signals of at least one sensor can be used and taken as an analog signal directly at the output of the electrical amplification circuit after the piezo element. Furthermore, a high-resolution digital ultrasonic time signal can then be generated by means of an analog-to-digital conversion according to a predetermined sampling theorem, for example according to Shannon, the sampling can typically take place at >=100 kHz, preferably 200 kHz."""
        },
        "WO2025131593A1": {
            "title": "Ultrasonic sensor and method for producing an ultrasonic sensor",
            "abstract": """The ultrasonic sensors with microelectromechanical systems (MEMS) face challenges in manufacturing and integration with control circuits. The ultrasonic sensor utilizes a flexible connecting section to bridge the spatial distance between circuit carriers, allowing for efficient electrical connection while accommodating movement and reducing rigidity.""",
            "claims": """1. An ultrasonic sensor comprising: a microelectromechanical sensor unit designed as a piezoelectric ultrasonic transducer. 2. The ultrasonic sensor according to claim 1, wherein the membrane component is integrally connected to the sensor component by adhesive bonding.""",
            "description": """According to one embodiment, the microelectromechanical sensor unit can be designed as a piezoelectric ultrasonic transducer. The membrane components can be embedded in the housing base and each have an oscillatory plastic membrane for ultrasound-based interaction with the environment of the ultrasonic sensor. The signal processing unit forms a control circuit of the ultrasonic sensor, embodied as an ASIC."""
        }
    }
    
    return patents

def create_mock_extraction_results(patent_data: Dict[str, Any], taxonomies: List[str]) -> Dict[str, MockTaxonomyResult]:
    """Create mock extraction results based on patent content"""
    
    results = {}
    combined_text = f"{patent_data['abstract']} {patent_data['claims']} {patent_data['description']}"
    
    for taxonomy in taxonomies:
        extracted_chunks = []
        confidence_scores = []
        source_spans = []
        
        # Mock extraction logic based on keyword matching
        if taxonomy == "Piezoelectric":
            if "piezo" in combined_text.lower():
                extracted_chunks = [
                    "ultrasonic time signals of at least one sensor can be used and taken as an analog signal directly at the output of the electrical amplification circuit after the piezo element",
                    "microelectromechanical sensor unit can be designed as a piezoelectric ultrasonic transducer"
                ]
                confidence_scores = [0.92, 0.88]
                source_spans = [(150, 280), (320, 420)]
        
        elif taxonomy == "MEMS":
            if "mems" in combined_text.lower() or "microelectromechanical" in combined_text.lower():
                extracted_chunks = [
                    "microelectromechanical systems (MEMS) face challenges in manufacturing and integration",
                    "microelectromechanical sensor unit designed as a piezoelectric ultrasonic transducer"
                ]
                confidence_scores = [0.85, 0.90]
                source_spans = [(50, 150), (200, 300)]
        
        elif taxonomy == "Digital Signal Processing":
            if "signal processing" in combined_text.lower() or "digital" in combined_text.lower():
                extracted_chunks = [
                    "high-resolution digital ultrasonic time signal can then be generated by means of an analog-to-digital conversion",
                    "signal processing unit forms a control circuit of the ultrasonic sensor, embodied as an ASIC"
                ]
                confidence_scores = [0.87, 0.83]
                source_spans = [(400, 520), (600, 720)]
        
        elif taxonomy == "Object Detection":
            if "object" in combined_text.lower() or "detection" in combined_text.lower():
                extracted_chunks = [
                    "method and device for ultrasound-based object classification using multiple ultrasonic sensors",
                    "ultrasound-based interaction with the environment of the ultrasonic sensor"
                ]
                confidence_scores = [0.91, 0.79]
                source_spans = [(80, 180), (500, 600)]
        
        elif taxonomy == "Parking Systems":
            if "parking" in combined_text.lower() or "automotive" in combined_text.lower():
                extracted_chunks = []  # No parking-specific content in these patents
                confidence_scores = []
                source_spans = []
        
        # Create result object
        results[taxonomy] = MockTaxonomyResult(
            taxonomy=taxonomy,
            extracted_chunks=extracted_chunks,
            confidence_scores=confidence_scores,
            source_spans=source_spans,
            context=f"Mock extraction for {taxonomy} - found {len(extracted_chunks)} relevant chunks"
        )
    
    return results

def generate_mock_dataframe(all_results: Dict[str, Dict[str, MockTaxonomyResult]]) -> pd.DataFrame:
    """Generate DataFrame from mock results"""
    
    rows = []
    
    for patent_number, patent_results in all_results.items():
        for taxonomy, result in patent_results.items():
            if result.extracted_chunks:
                # Create a row for each extracted chunk
                for i, chunk in enumerate(result.extracted_chunks):
                    row = {
                        'patent_number': patent_number,
                        'taxonomy': taxonomy,
                        'extracted_text': chunk,
                        'confidence_score': result.confidence_scores[i] if i < len(result.confidence_scores) else 0.0,
                        'source_span_start': result.source_spans[i][0] if i < len(result.source_spans) else 0,
                        'source_span_end': result.source_spans[i][1] if i < len(result.source_spans) else 0,
                        'context': result.context,
                        'chunk_length': len(chunk)
                    }
                    rows.append(row)
            else:
                # Create a row indicating no results found
                row = {
                    'patent_number': patent_number,
                    'taxonomy': taxonomy,
                    'extracted_text': '',
                    'confidence_score': 0.0,
                    'source_span_start': 0,
                    'source_span_end': 0,
                    'context': result.context,
                    'chunk_length': 0
                }
                rows.append(row)
    
    return pd.DataFrame(rows)

def generate_demo_report(df: pd.DataFrame, all_results: Dict[str, Dict[str, MockTaxonomyResult]]):
    """Generate demo summary report"""
    
    print("\n" + "="*60)
    print("📊 DEMO EXTRACTION SUMMARY REPORT")
    print("="*60)
    
    # Overall statistics
    total_patents = len(all_results)
    total_taxonomies = len(df['taxonomy'].unique())
    total_extractions = len(df[df['extracted_text'] != ''])
    
    print(f"📄 Patents processed: {total_patents}")
    print(f"🏷️  Taxonomies analyzed: {total_taxonomies}")
    print(f"✅ Successful extractions: {total_extractions}")
    print(f"📈 Success rate: {(total_extractions / (total_patents * total_taxonomies) * 100):.1f}%")
    
    # Top taxonomies by extraction count
    print(f"\n🔝 TOP TAXONOMIES BY EXTRACTION COUNT:")
    taxonomy_counts = df[df['extracted_text'] != ''].groupby('taxonomy').size().sort_values(ascending=False)
    for taxonomy, count in taxonomy_counts.items():
        print(f"   {taxonomy}: {count} extractions")
    
    # Patent-wise results
    print(f"\n📋 PATENT-WISE RESULTS:")
    for patent_number, patent_results in all_results.items():
        successful_extractions = sum(1 for result in patent_results.values() if result.extracted_chunks)
        total_taxonomies_for_patent = len(patent_results)
        success_rate = (successful_extractions / total_taxonomies_for_patent * 100) if total_taxonomies_for_patent > 0 else 0
        
        print(f"   {patent_number}: {successful_extractions}/{total_taxonomies_for_patent} taxonomies ({success_rate:.1f}%)")
    
    # Average confidence scores
    if not df[df['confidence_score'] > 0].empty:
        avg_confidence = df[df['confidence_score'] > 0]['confidence_score'].mean()
        print(f"\n🎯 Average confidence score: {avg_confidence:.2f}")
    
    # Sample extractions
    print(f"\n📝 SAMPLE EXTRACTIONS:")
    for _, row in df[df['extracted_text'] != ''].head(3).iterrows():
        print(f"   Patent: {row['patent_number']}")
        print(f"   Taxonomy: {row['taxonomy']}")
        print(f"   Text: {row['extracted_text'][:100]}...")
        print(f"   Confidence: {row['confidence_score']:.2f}")
        print()

def main():
    """Main demo function"""
    print("🚀 DEMO: Ultrasonic Sensor Taxonomy Extraction")
    print("This demo shows how the system works with mock data")
    print("="*60)
    
    # Define taxonomies based on your data structure
    taxonomies = [
        "Piezoelectric",
        "MEMS", 
        "Digital Signal Processing",
        "Object Detection",
        "Parking Systems",
        "Distance Measurement",
        "Noise Reduction",
        "PZT Material"
    ]
    
    # Create mock data
    patents = create_mock_ultrasonic_data()
    
    # Process each patent
    all_results = {}
    for patent_number, patent_data in patents.items():
        print(f"Processing patent: {patent_number}")
        results = create_mock_extraction_results(patent_data, taxonomies)
        all_results[patent_number] = results
    
    # Generate DataFrame
    df = generate_mock_dataframe(all_results)
    
    # Save results
    os.makedirs("results", exist_ok=True)
    output_path = "results/demo_ultrasonic_extraction.csv"
    df.to_csv(output_path, index=False)
    
    # Generate report
    generate_demo_report(df, all_results)
    
    print(f"\n💾 Demo results saved to: {output_path}")
    print(f"📊 Total results: {len(df)} rows")
    
    print("\n" + "="*60)
    print("🔧 TO USE WITH REAL API:")
    print("="*60)
    print("1. Get a valid Google API key from: https://aistudio.google.com/app/apikey")
    print("2. Update your .env file with: GOOGLE_API_KEY=your_actual_key")
    print("3. Run: python ultrasonic_taxonomy_extraction.py")
    print("4. The system will extract real taxonomies from patent data")
    
    print("\n📋 EXPECTED OUTPUT FORMAT:")
    print("The real system will produce similar results but with:")
    print("- Actual LLM-powered extraction (not keyword matching)")
    print("- Higher accuracy and better context understanding")
    print("- Confidence scores based on model certainty")
    print("- Interactive HTML reports with highlighted text")

if __name__ == "__main__":
    main()
