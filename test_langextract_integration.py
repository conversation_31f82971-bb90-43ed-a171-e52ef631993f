#!/usr/bin/env python3
"""
Test script for LangExtract Patent Taxonomy Extraction
This script demonstrates the complete workflow with real patent data.
"""

import os
import sys
import logging
from typing import List
import pandas as pd

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from patent_langextract import (
    PatentLangExtractProcessor, 
    extract_taxonomies_from_patent,
    extract_taxonomies_from_text
)
from config import Config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_basic_functionality():
    """Test basic functionality with a simple example"""
    print("=" * 60)
    print("Testing Basic LangExtract Functionality")
    print("=" * 60)
    
    # Test with sample text
    sample_abstract = """
    This invention relates to methods for genome recoding in bacterial cells. 
    The method involves reassigning amber stop codons to encode non-canonical amino acids,
    thereby creating genomically recoded organisms with enhanced properties.
    """
    
    sample_claims = """
    1. A method for creating a genomically recoded organism comprising:
       a) introducing a modified genetic code into a bacterial cell;
       b) reassigning amber stop codons to encode non-canonical amino acids;
       c) selecting cells with successful genome recoding.
    """
    
    taxonomies = [
        "Genome Recoding",
        "Non-canonical amino acids (ncAA)",
        "Synthetic organism/ genomically recoded organisms (GRO)"
    ]
    
    try:
        results = extract_taxonomies_from_text(
            abstract=sample_abstract,
            claims=sample_claims,
            taxonomies=taxonomies,
            patent_number="TEST001"
        )
        
        print("✅ Basic functionality test passed!")
        print(f"Results keys: {list(results.keys())}")
        
        for key, value in results.items():
            if key.endswith('_text') and value:
                print(f"  - {key}: {value[:100]}...")
                
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {str(e)}")
        return False

def test_real_patent_data():
    """Test with real patent data from the API"""
    print("\n" + "=" * 60)
    print("Testing with Real Patent Data")
    print("=" * 60)
    
    # Test patent numbers (these should exist in the API)
    test_patents = ["US11408007B2", "US11649450B2"]
    
    taxonomies = [
        "Genome Recoding",
        "Synthetic organism/ genomically recoded organisms (GRO)",
        "Non-canonical amino acids (ncAA)",
        "Orthogonal translation system (OTS)"
    ]
    
    processor = PatentLangExtractProcessor()
    
    for patent_number in test_patents:
        try:
            print(f"\nTesting patent: {patent_number}")
            
            # Test fetching patent data
            patent_data = processor.fetch_patent_data(patent_number)
            print(f"  ✅ Successfully fetched patent data")
            print(f"  - Title: {patent_data.title[:100] if patent_data.title else 'N/A'}...")
            print(f"  - Abstract length: {len(patent_data.abstract)} chars")
            print(f"  - Claims length: {len(patent_data.claims)} chars")
            
            # Test extraction
            results = processor.process_patent_with_taxonomies(
                patent_number=patent_number,
                taxonomies=taxonomies[:2]  # Test with first 2 taxonomies only
            )
            
            print(f"  ✅ Successfully processed taxonomies")
            
            for taxonomy, result in results.items():
                print(f"  - {taxonomy}: {len(result.extracted_chunks)} chunks extracted")
                if result.extracted_chunks:
                    print(f"    Sample: {result.extracted_chunks[0][:100]}...")
            
            return True
            
        except Exception as e:
            print(f"  ❌ Error with patent {patent_number}: {str(e)}")
            continue
    
    return False

def test_batch_processing():
    """Test batch processing functionality"""
    print("\n" + "=" * 60)
    print("Testing Batch Processing")
    print("=" * 60)
    
    patent_numbers = ["US11408007B2", "US11649450B2"]
    taxonomies = ["Genome Recoding", "Non-canonical amino acids (ncAA)"]
    
    try:
        processor = PatentLangExtractProcessor()
        
        # Test batch processing
        all_results = processor.extract_patent_taxonomies_batch(
            patent_numbers=patent_numbers,
            taxonomies=taxonomies
        )
        
        print(f"✅ Batch processing completed!")
        print(f"Processed {len(all_results)} patents")
        
        # Test DataFrame export
        df = processor.export_results_to_dataframe(all_results)
        print(f"✅ DataFrame export successful: {len(df)} rows")
        
        # Save test results
        os.makedirs(Config.OUTPUT_DIR, exist_ok=True)
        output_path = os.path.join(Config.OUTPUT_DIR, "test_results.csv")
        df.to_csv(output_path, index=False)
        print(f"✅ Results saved to: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Batch processing test failed: {str(e)}")
        return False

def test_convenience_functions():
    """Test convenience functions"""
    print("\n" + "=" * 60)
    print("Testing Convenience Functions")
    print("=" * 60)
    
    try:
        # Test the convenience function
        results = extract_taxonomies_from_patent(
            patent_number="US11408007B2",
            taxonomies=["Genome Recoding"],
            model_id=Config.DEFAULT_GEMINI_MODEL
        )
        
        print("✅ Convenience function test passed!")
        print(f"Result keys: {list(results.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Convenience function test failed: {str(e)}")
        return False

def run_comprehensive_test():
    """Run all tests"""
    print("🧪 Starting Comprehensive LangExtract Integration Tests")
    print(f"Using Gemini API Key: {'✅ Set' if Config.GEMINI_API_KEY else '❌ Missing'}")
    print(f"Using OpenAI API Key: {'✅ Set' if Config.OPENAI_API_KEY else '❌ Missing'}")
    print(f"Patent API URL: {Config.PATENT_API_BASE_URL}")
    
    tests = [
        ("Basic Functionality", test_basic_functionality),
        ("Real Patent Data", test_real_patent_data),
        ("Batch Processing", test_batch_processing),
        ("Convenience Functions", test_convenience_functions)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tests passed! LangExtract integration is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")
    
    return passed == total

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
