# LangExtract Patent Taxonomy Extraction

This implementation integrates Google's LangExtract library with the existing patent taxonomy classification system to provide advanced, taxonomy-aware text extraction from patent documents.

## Features

- **Taxonomy-Aware Extraction**: Automatically identifies and extracts text segments relevant to specific taxonomies
- **Multiple LLM Support**: Works with Gemini, OpenAI GPT models, and local models via Ollama
- **Patent API Integration**: Seamlessly fetches patent data from the Patent WR API
- **Batch Processing**: Process multiple patents and taxonomies efficiently
- **Interactive Visualization**: Generate HTML reports with highlighted extractions
- **Confidence Scoring**: Each extraction includes confidence scores for quality assessment
- **Flexible Configuration**: Easy configuration via environment variables

## Installation

1. **Install LangExtract** (already done):
   ```bash
   pip install langextract
   ```

2. **Set up API Keys** in your `.env` file:
   ```env
   # For Gemini models (recommended)
   GOOGLE_API_KEY=your_gemini_api_key_here
   
   # For OpenAI models (alternative)
   OPENAI_API_KEY=your_openai_api_key_here
   
   # Patent API (already configured)
   PATENT_API_BASE_URL=https://api.patent.wissenresearch.com
   ```

3. **Get API Keys**:
   - **Gemini API**: Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
   - **OpenAI API**: Visit [OpenAI Platform](https://platform.openai.com/api-keys)

## Quick Start

### 1. Simple Extraction from Patent Number

```python
from patent_langextract import extract_taxonomies_from_patent

# Define taxonomies to extract
taxonomies = [
    "Genome Recoding",
    "Synthetic organism/ genomically recoded organisms (GRO)",
    "Non-canonical amino acids (ncAA)",
    "Orthogonal translation system (OTS)"
]

# Extract from a patent
results = extract_taxonomies_from_patent(
    patent_number="US11408007B2",
    taxonomies=taxonomies,
    model_id="gemini-2.5-flash"
)

# View results
for taxonomy in taxonomies:
    key = taxonomy.lower().replace(' ', '_').replace('/', '_')
    text_key = f"{key}_text"
    if results[text_key]:
        print(f"{taxonomy}: {results[text_key][:100]}...")
```

### 2. Extract from Custom Text

```python
from patent_langextract import extract_taxonomies_from_text

results = extract_taxonomies_from_text(
    abstract="Your patent abstract text here...",
    claims="Your patent claims text here...",
    taxonomies=["Genome Recoding", "Non-canonical amino acids (ncAA)"],
    patent_number="CUSTOM001"
)
```

### 3. Batch Processing

```python
from patent_langextract import PatentLangExtractProcessor

processor = PatentLangExtractProcessor()

# Process multiple patents
all_results = processor.extract_patent_taxonomies_batch(
    patent_numbers=["US11408007B2", "US11649450B2"],
    taxonomies=["Genome Recoding", "Non-canonical amino acids (ncAA)"]
)

# Export to CSV
df = processor.export_results_to_dataframe(all_results)
df.to_csv("extraction_results.csv", index=False)
```

## Configuration Options

The system can be configured via environment variables or the `Config` class:

```python
from config import Config

# Model settings
Config.DEFAULT_GEMINI_MODEL = "gemini-2.5-flash"  # or "gemini-2.5-pro"
Config.DEFAULT_OPENAI_MODEL = "gpt-4o"

# Processing settings
Config.DEFAULT_CHUNK_SIZE = 4000  # Text chunk size
Config.EXTRACTION_PASSES = 2      # Multiple passes for better recall
Config.MAX_WORKERS = 4            # Parallel processing workers
Config.MIN_CONFIDENCE_THRESHOLD = 0.6  # Minimum confidence for results
```

## Advanced Usage

### Custom Processor Configuration

```python
from patent_langextract import PatentLangExtractProcessor

processor = PatentLangExtractProcessor(
    model_id="gemini-2.5-pro",  # Use more powerful model
    chunk_size=3000,            # Smaller chunks for precision
    overlap_size=300            # More overlap for context
)
```

### Generate HTML Visualization Reports

```python
# Process a patent
results = processor.process_patent_with_taxonomies(
    patent_number="US11408007B2",
    taxonomies=["Genome Recoding"]
)

# Generate interactive HTML report
patent_data = processor.fetch_patent_data("US11408007B2")
processor.generate_visualization_report(
    results=results,
    patent_data=patent_data,
    output_path="report.html"
)
```

## Testing

Run the comprehensive test suite:

```bash
python test_langextract_integration.py
```

Run the example usage:

```bash
python example_usage.py
```

## Integration with Existing Code

The LangExtract integration is designed to work alongside your existing extraction methods:

```python
# Use with existing extraction_methods.py patterns
from patent_langextract import extract_taxonomies_from_text
import extraction_methods

# Get patent data
abstract = "..."
claims = "..."

# Use existing methods
active_materials = extraction_methods.extract_active_materials(abstract, claims)

# Use LangExtract for taxonomies
taxonomy_results = extract_taxonomies_from_text(
    abstract=abstract,
    claims=claims,
    taxonomies=["Genome Recoding", "Non-canonical amino acids (ncAA)"]
)

# Combine results
combined_results = {**active_materials, **taxonomy_results}
```

## Model Recommendations

- **gemini-2.5-flash**: Fast, cost-effective, good for most use cases
- **gemini-2.5-pro**: More accurate for complex taxonomies, slower
- **gpt-4o**: Alternative to Gemini, requires OpenAI API key

## Troubleshooting

### API Key Issues
- Ensure your API key is valid and has sufficient quota
- Check that the key is properly set in your `.env` file
- For Gemini: Use `GOOGLE_API_KEY` (not `GEMINI_API_KEY`)

### Patent API Issues
- Verify patent numbers are in the correct format (e.g., "US11408007B2")
- Check internet connectivity
- Ensure the Patent WR API is accessible

### Performance Issues
- Reduce `chunk_size` for faster processing
- Decrease `extraction_passes` for speed
- Use `gemini-2.5-flash` instead of `gemini-2.5-pro`

## Output Format

The extraction results include:

- **extracted_chunks**: List of relevant text segments
- **confidence_scores**: Confidence score (0-1) for each chunk
- **source_spans**: Character positions in the original text
- **context**: Processing information and metadata

## File Structure

```
├── patent_langextract.py          # Main implementation
├── config.py                      # Configuration settings
├── test_langextract_integration.py # Test suite
├── example_usage.py               # Usage examples
├── requirements.txt               # Dependencies (updated)
└── README_LANGEXTRACT.md         # This file
```

## Next Steps

1. **Test the Implementation**: Run `python test_langextract_integration.py`
2. **Try Examples**: Run `python example_usage.py`
3. **Integrate with Your Workflow**: Use the convenience functions in your existing code
4. **Customize Taxonomies**: Modify the taxonomy lists for your specific use case
5. **Scale Up**: Use batch processing for large datasets

For questions or issues, refer to the [LangExtract documentation](https://github.com/google/langextract) or check the test files for working examples.
