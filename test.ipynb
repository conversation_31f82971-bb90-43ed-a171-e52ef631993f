#%%
import pandas as pd
#%%
df = pd.read_excel("patent.xlsx")
df.head(3)
#%%
list(df[0])
#%%
# df[0] = df[0].replace(r"\n+" , "" , regex = True)
#%%
for d in df[0]:
    print(d)
#%%
# df.to_excel("patent.xlsx" , index=False)
#%%
import requests
patent_data_url = "https://api.patent.wissenresearch.com/patent/detailed/"
patent_data_url += "US9300723B2"
print(patent_data_url)
data= requests.get(patent_data_url)
#%%
data.json()['abstract']
#%%
data.json()['claims']
#%%
"""You are an expert in legal text analysis and extraction dealing with patents and legal documents.
Your task is to extract the negative electrode and positive electrode active materials from the provided patent abstract and claims. The active material is typically a chemical compound or a mixture of compounds that plays a crucial role in the functionality of the invention described in the patent.
You will be provided with a patent abstract and claims. Your response should be a JSON object containing the following fields:
{
    "negative_electrode_active_material": "The  negative active material extracted from the patent abstract and claims."
    "positive_electrode_active_material": "The positive active material extracted from the patent abstract and claims."
    "llm_comments": "Provide any other llm comments that you think are relevant to the extraction of the active materials in this field, don't use any other fields for your comments as the required response is in JSON format."
}
"""
#%%
from langchain_core.output_parsers import JsonOutputParser
parser = JsonOutputParser)
#%%
import pandas as pd
df =pd.read_excel("results.xlsx")
df.head(2)
#%%
df.head(3)
#%%
respose_json = {
    "negative_electrode_active_material": "No relevant information provided regarding negative electrode active materials.",
    "positive_electrode_active_material": "No relevant information provided regarding positive electrode active materials.",
    "llm_comments": "The provided patent abstract and claims do not contain any information related to electrode active materials. The patent focuses on a mold for producing molded articles with specific gas venting features and does not pertain to electrochemical cells or batteries where electrode active materials would typically be relevant."
}
#%%
list(respose_json.keys())
#%%
output_json = {}
for key in list(respose_json.keys()):
    output_json[key] = respose_json.get(key , "")
output_json
#%%
import os
import json
file_path = "results.xlsx"
if os.path.exists(file_path):
    df = pd.read_excel(file_path)
    df = df.where(pd.notnull(df), None)
    data  = df.to_dict(orient="records")
    safe_data = json.loads(json.dumps(data))
    print({"status" : "completed" , "message" : safe_data})
    
#%%
import pandas as pd
df = pd.read_excel("results.xlsx")
df = df.where(pd.notnull(df), None)
data  = df.to_dict(orient="records")
data
#%%
try:
    print(a)
except Exception as e:
    print(e.info)

#%%

# df = pd.read_excel("/Users/<USER>/Downloads/Wissen Research_Landscape 2nd Interim Report_Synthetic Biology (1).xlsx" , sheet_name="Identified Patents")
# df.head(2)
#%%
new_df = pd.DataFrame(df['Publication_number'].tolist()[:3])
#%%
def apply_b2(paten):
    return paten + "B2"
new_df[0]= new_df[0].apply(apply_b2)
new_df
#%%
import requests
full_url = f"https://api.patent.wissenresearch.com/patent/detailed/{new_df[0][1]}"
data = requests.get(full_url)
#%%
claims , abstract = data.json()['claims'] , data.json()['abstract']

#%%
abstract
#%%
claims
#%%
from IPython.display import display, HTML

long_text = """(27)We claim:1. A genomically recoded organism (GRO) comprising a genome wherein at least one codon is reassigned creating an available sense codon for a recombinant tRNA;wherein at least one instance of the available sense codon is present as a sense codon in the coding region of at least one endogenous essential gene of interest that encodes a protein whose expression is needed for the GRO's viability;wherein the at least one instance of the available sense codon encodes a conserved amino acid residue at a functional site of the protein; andwherein the recombinant tRNA can be charged by a paired recombinant aminoacyl-tRNA synthetase (aaRS) to permit site-specific incorporation of a synthetic amino acid (sAA) or non-standard amino acid (nsAA) into a nascent peptide chain of the protein during translation of the transcribed essential gene in the GRO.2. The GRO ofclaim 1, whereinall genomic instances of a first stop codon sequence have been reassigned to a second stop codon sequence and the available sense codon consists of the sequence of the first stop codon, orall genomic instances of an endogenous sense codon sequence have been reassigned to a synonymous codon sequence and the available sense codon consists of the sequence of the endogenous sense codon;and wherein the anticodon sequence for the recombinant tRNA recognizes the available sense codon sequence.3. The GRO ofclaim 2, wherein the genomic sequence of the first stop codon sequence is TAG and the second stop codon sequence is TAA or TGA.4. The GRO of claim,3wherein the GRO is a variant ofE. colistrain C321.A A (GenBank accession CP006698).5. The GRO ofclaim 1further comprising a nucleic acid encoding an expression control sequence operably linked to a sequence encoding the recombinant aminoacyl-tRNA synthetase (aaRS) and the recombinant tRNA,wherein the GRO has reduced viability in non-permissive media that does not include the synthetic amino acid (sAA) or non-standard amino acid (nsAA) compared to permissive media including the synthetic amino acid (sAA) or non-standard amino acid (nsAA),wherein the escape frequency of the GRO in the non-permissive media is about 10−11or less.6. The GRO ofclaim 1wherein the essential gene of interest is selected from the group consisting of dnaX, lspA, secY, serS, murG, dnaA, adk, nadE, ribA, and gmk.7. The GRO ofclaim 2, wherein the gene or genes encoding the endogenous cognate translation machinery corresponding to the at least one reassigned codon is interrupted or deleted.8. The GRO ofclaim 1, wherein the available sense codon is only present in the essential gene of interest.9. The GRO ofclaim 7, further comprising a nucleic acid encoding an expression control sequence operably linked to a sequence encoding the recombinant aminoacyl-tRNA synthetase (aaRS), the recombinant tRNA.10. The GRO ofclaim 9, wherein the nucleic acid is episomal, extrachromosomal, or integrated into a chromosome of the GRO.11. The GRO ofclaim 10, wherein the genomic sequence of the available sense codon is TAG, the recombinant tRNA comprises the anticodon for UAG, and the corresponding release factor or factors is deleted or interrupted.12. The GRO ofclaim 9, wherein culturing the GRO with permissive media including the synthetic amino acid (sAA) or non-standard amino acid (nsAA) results in translation of the full-length protein encoded by the essential gene.13. The GRO ofclaim 12, wherein culturing the GRO with non-permissive media that does not include the synthetic amino acid (sAA) or non-standard amino acid (nsAA) results in truncation of the full-length protein encoded by the essential gene.14. The GRO ofclaim 12, wherein the GRO has reduced viability when cultured with non-permissive media that does not include the synthetic amino acid (sAA) or non-standard amino acid (nsAA) compared to permissive media including the synthetic amino acid (sAA) or non-standard amino acid (nsAA) that results in translation of the full-length protein encoded by the essential gene.15. The GRO ofclaim 14, wherein the escape frequency of the GRO when cultured with non-permissive media is 10−6or lower and the fitness of the GRO when cultured with permissive media is at least 70% of its parental strain grown under the same or similar conditions.16. The GRO ofclaim 1, wherein two or more instances of the available sense codon are present as sense codons in at least one of the essential gene of interest.17. The GRO ofclaim 1, wherein at least one instance of the available sense codon is present as a sense codon in at least a second endogenous essential gene of interest.18. The GRO ofclaim 1, wherein the essential gene is one that cannot be complemented by cross-feeding of metabolites to the GRO.19. The GRO ofclaim 1, where the at least one endogenous essential gene of interest is present only at its genomic locus.20. The GRO ofclaim 1, wherein the protein is functional when the synthetic amino acid (sAA) or non-standard amino acid (nsAA) is substituted for the endogenously encoded cognate amino acid.21. The GRO ofclaim 20, where the conserved residue is a tyrosine, phenylalanine or tryptophan.22. The GRO ofclaim 1, wherein the at least one instance of the available sense codon is present at the N-terminal end of the protein.23. The GRO ofclaim 21, wherein the synthetic amino acid or non-standard amino acid is pAcF (p-acetylphenylalanine), pIF (p-iodo-L-phenylalanine), or pAzF (p-azido-L-phenyalanine).24. A genomically recoded organism (GRO) comprising a nucleic acid encoding a recombinant aminoacyl-tRNA synthetase (aaRS), a nucleic acid encoding a recombinant tRNA, and a genome wherein at least one codon is reassigned creating an available sense codon for a recombinant tRNA and the gene or genes encoding the endogenous cognate translation machinery corresponding to the at least one reassigned codon is interrupted or deleted;at least one instance of the available sense codon is present as a sense codon in the coding region of three or more endogenous essential genes of interest that encode proteins whose expression is needed for the GRO's viability; andwherein the recombinant tRNA can be charged by a paired recombinant aminoacyl-tRNA synthetase (aaRS) to permit site-specific incorporation of a synthetic amino acid (sAA) or non-standard amino acid (nsAA) into a nascent peptide chain of the proteins during translation of the transcribed essential genes in the GRO.25. The GRO ofclaim 24, wherein the available sense codons are present as sense codons at conserved amino acid residues at functional sites of the endogenous essential genes having at least one instance of the available sense codon.26. The GRO ofclaim 25, wherein one or more of the essential genes having at least one instance of the available sense codon encodes a protein whose function cannot be complemented by cross-feeding of metabolites to the GRO.27. The GRO ofclaim 26, wherein the escape frequency of the GRO cultured with non-permissive media is 10−8or lower and the fitness of the GRO when cultured with permissive media is at least 70% of its parental strain grown under the same conditions."""
display(HTML(f"<div style='white-space: normal; word-wrap: break-word; width:100%'>{long_text}</div>"))

#%%
data.json()['claims']
#%%
new_df.to_excel("genomo_recording.xlsx")
#%%
len(new_df)
#%%
import pandas as pd
df = pd.read_excel("/Users/<USER>/Downloads/prashant_sharing.xlsx")
df.head(10)
#%%
new_taxonomy = []
taxonomy = list(df.columns[1:])
for tax in taxonomy:
    tax = tax.strip()
    new_taxonomy.append(tax)
new_taxonomy
#%%
from google import genai

GOOGLE_API_KEY="AIzaSyCZD-FosC4r6XqxFeVerL9_hqmTfPTurGo"
MODEL_ID="gemini-2.5-flash-preview-04-17"
client = genai.Client(api_key = GOOGLE_API_KEY)
#%%
from pydantic import BaseModel
class output_format(BaseModel):
    prompt : str
#%%

from google.genai import types
def gemini_call(taxonomy):
    response = client.models.generate_content(
        model = "gemini-2.5-flash" , 
        contents = """I will provide you category , you have to return me a prompt in the defined prompt below.

        Example start : 
        Input : ['negative_electrode' , 'positive_electrode' , 'electrodes_llm_comments', 'binder_fibrilizable', 'binder_non_fibrilizable', 'binder_other', 'binder_particle_size', 'binder_thickness', 'binder_porosity', 'binder_llm_comments']

        prompt : "Your task is to extract below mentioned information to best of your knowledge.
    
You will be provided with a patent abstract and claims. Your response should be a JSON object containing the following fields:
{
"negative_electrode_active_material": "The negative active material extracted from the patent abstract and claims."
"positive_electrode_active_material": "The positive active material extracted from the patent abstract and claims."
"electrodes_llm_comments": "Provide any other llm comments that you think are relevant to the extraction of the active materials in this field, don't use any other fields for your comments as the required response is in JSON format.",
"binder_fibrilizable": "The fibrilizable binders (such as Polytetrafluoroethylene (PTFE), Polypropylene (PP), Co-polymers, Polylactic acid, Polymer blends) extracted from the patent abstract and claims.",
"binder_non_fibrilizable": "The non-fibrilizable binders (such as PVDF, PEO, Polypropylene carbonate, Acrylonitrile-butadiene-styrene (ABS), Hydrogenated nitrile butadiene rubber, Paraffin wax, cellulose, cellulose derivative) extracted from the patent abstract and claims.",
"binder_other": "Any other binders extracted from the patent abstract and claims.",
"binder_particle_size": "The particle size of the binder extracted from the patent abstract and claims.",
"binder_thickness": "The thickness of the binder extracted from the patent abstract and claims.",
"binder_porosity": "The porosity of the binder extracted from the patent abstract and claims.",
"binder_llm_comments": "Provide any other llm comments that you think are relevant to the extraction of the binder in this field, don't use any other fields for your comments as the required response is in JSON format."
.
.
.
.
.
.
}

Extract all taxonomy from the following patent abstract and claims.

Example End.
""" + f"""

Input: {taxonomy}

""", 
        config = types.GenerateContentConfig(
            system_instruction="You are expert in creating prompt for taxonomy provided." ,
            thinking_config=types.ThinkingConfig(thinking_budget=0 , include_thoughts=True) , 
            temperature=0.1 , 
            response_mime_type="application/json" , 
            response_schema=list[output_format]) 
    )

    return response
#%%
print(new_taxonomy)
respo = gemini_call(new_taxonomy)
#%%
respo
#%%
import json
print(json.loads(respo.parsed[0].json())['prompt'])
#%%
"""Your task is to extract below mentioned information to best of your knowledge. The active material is typically a chemical compound or a mixture of compounds that plays a crucial role in the functionality of the invention described in the patent.
Binders are typically polymeric or organic compounds that help hold together the components of the electrode or other parts of the invention described in the patent.
You will be provided with a patent abstract and claims. Your response should be a JSON object containing the following fields:
{
    "negative_electrode_active_material": "The  negative active material extracted from the patent abstract and claims."
    "positive_electrode_active_material": "The positive active material extracted from the patent abstract and claims."
    "electrodes_llm_comments": "Provide any other llm comments that you think are relevant to the extraction of the active materials in this field, don't use any other fields for your comments as the required response is in JSON format.",
    .
    . 
    . 
    . 
    .
}

Extract all relevant category from the following patent abstract and claims."""
