openai_key = "Add your OpenAI API key here"
MODEL = "gpt-4o"

import os 
from openai import OpenAI
from dotenv import load_dotenv
import json
from langchain_core.output_parsers import JsonOutputParser
import pandas as pd

import prompts
api_key = openai_key

client = OpenAI(api_key=api_key)
parser = JsonOutputParser()


def extract_active_materials(abstract, claims):
    system_prompt = prompts.system_prompt_active_material
    user_prompt  = f"""Extract the negative and positive electrode active materials from the following patent abstract and claims.
    Patent Abstract: {abstract}\n
    Patent Claims: {claims}
    """

    completion = client.chat.completions.create(
    model=MODEL,
    messages=[
        {"role": "system", "content": system_prompt}, # <-- This is the system message that provides context to the model
        {"role": "user", "content": user_prompt}  # <-- This is the user message for which the model will generate a response
    ]
    )

    print("Assistant: " + completion.choices[0].message.content)
    response = completion.choices[0].message.content
    response_json = parser.parse(response)

    active_material_data = {
            "negative_electrode": response_json.get("negative_electrode_active_material", ""),
            "positive_electrode": response_json.get("positive_electrode_active_material", ""),
            "electrodes_llm_comments": response_json.get("llm_comments", "")
        }

    return active_material_data

def extract_additives(abstract, claims):
    system_prompt = prompts.system_prompt_additive
    user_prompt  = f"""Extract the additives from the following patent abstract and claims.
    Patent Abstract: {abstract}\n
    Patent Claims: {claims}
    """

    completion = client.chat.completions.create(
    model=MODEL,
    messages=[
        {"role": "system", "content": system_prompt}, # <-- This is the system message that provides context to the model
        {"role": "user", "content": user_prompt}  # <-- This is the user message for which the model will generate a response
    ]
    )

    print("Assistant: " + completion.choices[0].message.content)
    response = completion.choices[0].message.content
    response_json = parser.parse(response)

    additive_data = {
            "additive_carbon_black(CB)": response_json.get("additive_carbon_black(CB)", ""),
            "additive_carbon_nanotube(CNT)": response_json.get("additive_carbon_nanotube(CNT)", ""),
            "additive_activated_carbon(AC)": response_json.get("additive_activated_carbon(AC)", ""),
            "additive_particle_size": response_json.get("additive_particle_size", ""),
            "additive_other": response_json.get("additive_other", ""),
            "additives_llm_comments": response_json.get("llm_comments", "")
        }

    return additive_data


def extract_binders(abstract, claims):
    system_prompt = prompts.system_prompt_binder
    user_prompt  = f"""Extract the binders from the following patent abstract and claims.
    Patent Abstract: {abstract}\n
    Patent Claims: {claims}
    """

    completion = client.chat.completions.create(
    model=MODEL,
    messages=[
        {"role": "system", "content": system_prompt}, # <-- This is the system message that provides context to the model
        {"role": "user", "content": user_prompt}  # <-- This is the user message for which the model will generate a response
    ]
    )

    print("Assistant: " + completion.choices[0].message.content)
    response = completion.choices[0].message.content
    response_json = parser.parse(response)
    binder_data = {
            "binder_fibrilizable": response_json.get("binder_fibrilizable", ""),
            "binder_non_fibrilizable": response_json.get("binder_non_fibrilizable", ""),
            "binder_other": response_json.get("binder_other", ""),
            "binder_particle_size": response_json.get("binder_particle_size", ""),
            "binder_thickness": response_json.get("binder_thickness", ""),
            "binder_porosity": response_json.get("binder_porosity", ""),
            "binder_llm_comments": response_json.get("binder_llm_comments", "")
        }

    return binder_data

def exrtract_electrolyte(abstract, claims):
    system_prompt = prompts.system_prompt_electrolyte
    user_prompt  = f"""Extract the electrolyte from the following patent abstract and claims.
    Patent Abstract: {abstract}\n
    Patent Claims: {claims}
    """

    completion = client.chat.completions.create(
    model=MODEL,
    messages=[
        {"role": "system", "content": system_prompt}, # <-- This is the system message that provides context to the model
        {"role": "user", "content": user_prompt}  # <-- This is the user message for which the model will generate a response
    ]
    )

    print("Assistant: " + completion.choices[0].message.content)
    response = completion.choices[0].message.content
    response_json = parser.parse(response)

    electrolyte_data = {
        "electrolyte_material": response_json.get("electrolyte_material", ""),
        "electrolyte_thickness": response_json.get("electrolyte_thickness", ""),
        "electrolyte_llm_comments": response_json.get("electrolyte_llm_comments", "")
    }

    return electrolyte_data

def extract_current_collector(abstract, claims):
    system_prompt = prompts.system_prompt_current_collector
    user_prompt  = f"""Extract the current collector from the following patent abstract and claims.
    Patent Abstract: {abstract}\n
    Patent Claims: {claims}
    """

    completion = client.chat.completions.create(
    model=MODEL,
    messages=[
        {"role": "system", "content": system_prompt}, # <-- This is the system message that provides context to the model
        {"role": "user", "content": user_prompt}  # <-- This is the user message for which the model will generate a response
    ]
    )

    print("Assistant: " + completion.choices[0].message.content)
    response = completion.choices[0].message.content
    response_json = parser.parse(response)

    current_collector_data = {
        "current_collector_aluminium": response_json.get("current_collector_aluminium", ""),
        "current_collector_steel": response_json.get("current_collector_steel", ""),
        "current_collector_copper": response_json.get("current_collector_copper", ""),
        "current_collector_other": response_json.get("current_collector_other", ""),
        "current_collector_llm_comments": response_json.get("current_collector_llm_comments", "")
    }

    return current_collector_data

def extract_mixing_mass_ratio(abstract, claims):
    system_prompt = prompts.system_prompt_mixing_mass_ratio
    user_prompt  = f"""Extract the mixing mass ratio from the following patent abstract and claims.
    Patent Abstract: {abstract}\n
    Patent Claims: {claims}
    """

    completion = client.chat.completions.create(
    model=MODEL,
    messages=[
        {"role": "system", "content": system_prompt}, # <-- This is the system message that provides context to the model
        {"role": "user", "content": user_prompt}  # <-- This is the user message for which the model will generate a response
    ]
    )

    print("Assistant: " + completion.choices[0].message.content)
    response = completion.choices[0].message.content
    response_json = parser.parse(response)

    mixing_mass_ratio_data = {
        "mixing_mass_ratio_active_conductive_binder": response_json.get("mixing_mass_ratio_active_conductive_binder", ""),
        "mixing_mass_ratio_active_conductive_electrolyte_binder": response_json.get("mixing_mass_ratio_active_conductive_electrolyte_binder", ""),
        "mixing_mass_ratio_binder_to_binder": response_json.get("mixing_mass_ratio_binder_to_binder", ""),
        "mixing_mass_ratio_other": response_json.get("mixing_mass_ratio_other", ""),
        "mixing_mass_ratio_llm_comments": response_json.get("mixing_mass_ratio_llm_comments", "")
    }

    return mixing_mass_ratio_data

def extract_mixing(abstract, claims):
    system_prompt = prompts.system_prompt_mixing_method
    user_prompt  = f"""Extract the mixing information from the following patent abstract and claims.
    Patent Abstract: {abstract}\n
    Patent Claims: {claims}
    """

    completion = client.chat.completions.create(
    model=MODEL,
    messages=[
        {"role": "system", "content": system_prompt}, # <-- This is the system message that provides context to the model
        {"role": "user", "content": user_prompt}  # <-- This is the user message for which the model will generate a response
    ]
    )

    print("Assistant: " + completion.choices[0].message.content)
    response = completion.choices[0].message.content
    response_json = parser.parse(response)

    mixing_data = {
        "mixing_intensive_high_shear": response_json.get("mixing_intensive_high_shear", ""),
        "mixing_lower_shear": response_json.get("mixing_lower_shear", ""),
        "mixing_vortex": response_json.get("mixing_vortex", ""),
        "mixing_ball": response_json.get("mixing_ball", ""),
        "mixing_dry_extrusion": response_json.get("mixing_dry_extrusion", ""),
        "mixing_jet": response_json.get("mixing_jet", ""),
        "mixing_dry": response_json.get("mixing_dry", ""),
        "mixing_other": response_json.get("mixing_other", ""),
        "mixing_llm_comments": response_json.get("mixing_llm_comments", "")
    }

    return mixing_data

def extract_mixing_process_parameters(abstract, claims):
    system_prompt = prompts.system_prompt_mixing_process_parameters
    user_prompt  = f"""Extract the mixing process parameters from the following patent abstract and claims.
    Patent Abstract: {abstract}\n
    Patent Claims: {claims}
    """

    completion = client.chat.completions.create(
    model=MODEL,
    messages=[
        {"role": "system", "content": system_prompt}, # <-- This is the system message that provides context to the model
        {"role": "user", "content": user_prompt}  # <-- This is the user message for which the model will generate a response
    ]
    )

    print("Assistant: " + completion.choices[0].message.content)
    response = completion.choices[0].message.content
    response_json = parser.parse(response)

    mixing_process_parameters_data = {
        "rotation_speed": response_json.get("rotation_speed", ""),
        "grinding_mixing_pressure": response_json.get("grinding_mixing_pressure", ""),
        "mixing_temperature": response_json.get("mixing_temperature", ""),
        "mixing_process_parameters_llm_comments": response_json.get("mixing_process_parameters_llm_comments", "")
    }

    return mixing_process_parameters_data


def extract_process_film_production(abstract, claims):
    system_prompt = prompts.system_prompt_process_film_production
    user_prompt  = f"""Extract the process film production from the following patent abstract and claims.
    Patent Abstract: {abstract}\n
    Patent Claims: {claims}
    """

    completion = client.chat.completions.create(
    model=MODEL,
    messages=[
        {"role": "system", "content": system_prompt}, # <-- This is the system message that provides context to the model
        {"role": "user", "content": user_prompt}  # <-- This is the user message for which the model will generate a response
    ]
    )

    print("Assistant: " + completion.choices[0].message.content)
    response = completion.choices[0].message.content
    response_json = parser.parse(response)

    process_film_production_data = {
        "process_dry_spraying": response_json.get("process_dry_spraying", ""),
        "process_electrostatic_spray_deposition": response_json.get("process_electrostatic_spray_deposition", ""),
        "process_extrusion": response_json.get("process_extrusion", ""),
        "process_pressing_plate_pressing": response_json.get("process_pressing_plate_pressing", ""),
        "process_hot_rolling": response_json.get("process_hot_rolling", ""),
        "process_rolling_calendering": response_json.get("process_rolling_calendering", ""),
        "process_roll_to_roll": response_json.get("process_roll_to_roll", ""),
        "process_printing": response_json.get("process_printing", ""),
        "process_temperature_uv_curing": response_json.get("process_temperature_uv_curing", ""),
        "process_roller_speed": response_json.get("process_roller_speed", ""),
        "process_other": response_json.get("process_other", ""),
        "process_film_production_llm_comments": response_json.get("process_film_production_llm_comments", "")
    }

    return process_film_production_data


def extract_film_properties(abstract, claims):
    system_prompt = prompts.system_prompt_film_properties
    user_prompt  = f"""Extract the film properties from the following patent abstract and claims.
    Patent Abstract: {abstract}\n
    Patent Claims: {claims}
    """

    completion = client.chat.completions.create(
    model=MODEL,
    messages=[
        {"role": "system", "content": system_prompt}, # <-- This is the system message that provides context to the model
        {"role": "user", "content": user_prompt}  # <-- This is the user message for which the model will generate a response
    ]
    )

    print("Assistant: " + completion.choices[0].message.content)
    response = completion.choices[0].message.content
    response_json = parser.parse(response)

    film_properties_data = {
        "film_porosity": response_json.get("film_porosity", ""),
        "film_thickness": response_json.get("film_thickness", ""),
        "film_density": response_json.get("film_density", ""),
        "film_strength": response_json.get("film_strength", ""),
        "film_auto_supported_formation": response_json.get("film_auto_supported_formation", ""),
        "film_properties_llm_comments": response_json.get("film_properties_llm_comments", "")
    }

    return film_properties_data


def extract_lamination(abstract, claims):
    system_prompt = prompts.system_prompt_lamination
    user_prompt  = f"""Extract the lamination information from the following patent abstract and claims.
    Patent Abstract: {abstract}\n
    Patent Claims: {claims}
    """

    completion = client.chat.completions.create(
    model=MODEL,
    messages=[
        {"role": "system", "content": system_prompt}, # <-- This is the system message that provides context to the model
        {"role": "user", "content": user_prompt}  # <-- This is the user message for which the model will generate a response
    ]
    )

    print("Assistant: " + completion.choices[0].message.content)
    response = completion.choices[0].message.content
    response_json = parser.parse(response)

    lamination_data = {
        "lamination_colamination": response_json.get("lamination_colamination", ""),
        "lamination_film_deposition": response_json.get("lamination_film_deposition", ""),
        "lamination_film_adhesion": response_json.get("lamination_film_adhesion", ""),
        "lamination_hot_rolling_rolling": response_json.get("lamination_hot_rolling_rolling", ""),
        "lamination_plate_pressing": response_json.get("lamination_plate_pressing", ""),
        "lamination_other": response_json.get("lamination_other", ""),
        "lamination_llm_comments": response_json.get("lamination_llm_comments", "")
    }

    return lamination_data

def extract_electrode_structural_pattern(abstract, claims):
    system_prompt = prompts.system_prompt_electrode_structural_pattern
    user_prompt  = f"""Extract the electrode structure from the following patent abstract and claims.
    Patent Abstract: {abstract}\n
    Patent Claims: {claims}
    """

    completion = client.chat.completions.create(
    model=MODEL,
    messages=[
        {"role": "system", "content": system_prompt}, # <-- This is the system message that provides context to the model
        {"role": "user", "content": user_prompt}  # <-- This is the user message for which the model will generate a response
    ]
    )

    print("Assistant: " + completion.choices[0].message.content)
    response = completion.choices[0].message.content
    response_json = parser.parse(response)

    electrode_structure_data = {
        "electrode_structural_pattern": response_json.get("electrode_structural_pattern", ""),
        "electrode_structural_pattern_llm_comments": response_json.get("electrode_structural_pattern_llm_comments", "")
    }

    return electrode_structure_data

def extract_properties(abstract, claims):
    system_prompt = prompts.system_prompt_properties
    user_prompt  = f"""Extract the properties from the following patent abstract and claims.
    Patent Abstract: {abstract}\n
    Patent Claims: {claims}
    """

    completion = client.chat.completions.create(
    model=MODEL,
    messages=[
        {"role": "system", "content": system_prompt}, # <-- This is the system message that provides context to the model
        {"role": "user", "content": user_prompt}  # <-- This is the user message for which the model will generate a response
    ]
    )

    print("Assistant: " + completion.choices[0].message.content)
    response = completion.choices[0].message.content
    response_json = parser.parse(response)

    properties_data = {
        "property_pressure": response_json.get("property_pressure", ""),
        "property_thickness": response_json.get("property_thickness", ""),
        "property_density": response_json.get("property_density", ""),
        "property_strength": response_json.get("property_strength", ""),
        "property_electrode_conductivity": response_json.get("property_electrode_conductivity", ""),
        "property_electrode_cycle_life": response_json.get("property_electrode_cycle_life", ""),
        "property_retention_rate": response_json.get("property_retention_rate", ""),
        "properties_llm_comments": response_json.get("properties_llm_comments", "")
    }

    return properties_data

def extract_application(abstract, claims):
    system_prompt = prompts.system_prompt_application
    user_prompt  = f"""Extract the application from the following patent abstract and claims.
    Patent Abstract: {abstract}\n
    Patent Claims: {claims}
    """

    completion = client.chat.completions.create(
    model=MODEL,
    messages=[
        {"role": "system", "content": system_prompt}, # <-- This is the system message that provides context to the model
        {"role": "user", "content": user_prompt}  # <-- This is the user message for which the model will generate a response
    ]
    )

    print("Assistant: " + completion.choices[0].message.content)
    response = completion.choices[0].message.content
    response_json = parser.parse(response)

    application_data = {
        "application_automotive": response_json.get("application_automotive", ""),
        "application_electronics": response_json.get("application_electronics", ""),
        "application_other": response_json.get("application_other", ""),
        "application_llm_comments": response_json.get("application_llm_comments", "")
    }

    return application_data