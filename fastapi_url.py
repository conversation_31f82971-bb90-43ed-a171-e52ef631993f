from fastapi import FastAP<PERSON> , UploadFile , File , HTTPException , Form , BackgroundTasks , Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel , <PERSON>
from typing import Optional
import pandas as pd
import requests
import os
import logging
from openai import OpenAI
import json
import numpy as np
from google import genai
from google.genai import types
from langchain_core.output_parsers import JsonOutputParser
from dotenv import load_dotenv
load_dotenv()
app = FastAPI()


app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],           # specify allowed origin(s)
    allow_credentials=False,
    allow_methods=["*"],             # allow all HTTP methods
    allow_headers=["*"],             # allow all headers
)

api_key = os.getenv("openai_key")
parser = JsonOutputParser()
MODEL = os.getenv("MODEL_NAME")
client = OpenAI(api_key=api_key)
MODEL_GEMINI = os.getenv("MODEL_ID")
client_gemini = genai.Client(api_key = os.getenv("GOOGLE_API_KEY"))

class gemini_output_format(BaseModel):
    prompt : str

class Item(BaseModel):
    # system_message : str = Field(... , min_length = 3 , description = "Please Enter the system message")
    user_message : str = Field(... , min_length = 3 , description = "Please Enter the user message")


def gemini_call(taxonomy):
    response = client_gemini.models.generate_content(
        model = MODEL_GEMINI , 
        contents = """I will provide you category , you have to return me a prompt in the defined prompt below.
        I will also provide you some example of taxonomy , create a taxonomy defination based on the examples provided.
        
        Example start : 
        Input : ['negative_electrode' , 'positive_electrode' , 'binder_fibrilizable', 'binder_non_fibrilizable', 'binder_other', 'binder_particle_size', 'binder_thickness', 'binder_porosity']
        negative_electrode_example : []
        positive_electrode_example : []
        .
        .
        .
        .

        
        output : "Your task is to extract below mentioned information to best of your knowledge.
    
You will be provided with a patent abstract and claims. Your response should be a JSON object containing the taxonomy fields:
{
"negative_electrode_active_material": "The negative active material extracted from the patent abstract and claims."
"positive_electrode_active_material": "The positive active material extracted from the patent abstract and claims."
"binder_fibrilizable": "The fibrilizable binders (such as Polytetrafluoroethylene (PTFE), Polypropylene (PP), Co-polymers, Polylactic acid, Polymer blends) extracted from the patent abstract and claims.",
"binder_non_fibrilizable": "The non-fibrilizable binders (such as PVDF, PEO, Polypropylene carbonate, Acrylonitrile-butadiene-styrene (ABS), Hydrogenated nitrile butadiene rubber, Paraffin wax, cellulose, cellulose derivative) extracted from the patent abstract and claims.",
"binder_other": "Any other binders extracted from the patent abstract and claims.",
"binder_particle_size": "The particle size of the binder extracted from the patent abstract and claims.",
"binder_thickness": "The thickness of the binder extracted from the patent abstract and claims.",
"binder_porosity": "The porosity of the binder extracted from the patent abstract and claims.",
.
.
.
.
.
.
}

Extract all taxonomy from the following patent abstract and claims.

Example End.
""" + f"""

Input: {taxonomy}

""", 
        config = types.GenerateContentConfig(
            system_instruction="You are expert in creating prompt for taxonomy provided." ,
            thinking_config=types.ThinkingConfig(thinking_budget=0 , include_thoughts=True) , 
            temperature=0.1 , 
            response_mime_type="application/json" , 
            response_schema=list[gemini_output_format]) 
    )

    return response

@app.get("/")
async def ping():
    return {"ping" : "pong"}

@app.post("/create_prompt")
async def create_prompt(file : UploadFile = File(...)):
    df = pd.read_excel(file.file)
    new_taxonomy = []
    taxonomy = list(df.columns[1:])
    for tax in taxonomy:
        tax = tax.strip()
        new_taxonomy.append(tax)

    respo = gemini_call(new_taxonomy)
    prompt = json.loads(respo.parsed[0].json())['prompt']
    

    return {"status" : 200 , "prompt" : prompt}


@app.post("/taxanomy")
async def taxanomy(backgroundtasks : BackgroundTasks , user_message : str = Form(...) , file : UploadFile = File(...)):
    # system_message : str = Form(...)
    # item = Item(system_message = system_message , user_message = user_message)
    item = Item(user_message = user_message)
    # system_message = item.system_message
    system_message = """You are an expert in legal text analysis and extraction dealing with patents and legal documents."""
    user_message = item.user_message

    if not file.filename.endswith((".xlsx" , ".xls")):
        raise HTTPException(status_code = 400 , detail = "Invalid File Type uploaded") 
    
    # full_path = f"/tmp/{file.filename}"
    df = pd.read_excel(file.file)

    backgroundtasks.add_task(dataframe_modification , df , system_message ,user_message)

    return {"status" : 200 , "message" : "Received the Request Processing......" , "patent_number" : list(df[0])}
    # return {"output" : system_message+user_message , "dataframe" : df.head(2).to_dict()}


# def get_results_list():
#     return []

def dataframe_modification(df , system_message , user_message):
    # print(df.head(2))
    patent_url = os.getenv("patent_data_url")
    results = []
    for patent_number in df[0]:
        print(patent_number)
        full_url = patent_url + str(patent_number)
        try:
            data = requests.get(full_url)
        except:
            logging.warning(f"Skipping this patent number {patent_number}")

        claims , abstract = data.json()['claims'] , data.json()['abstract']

        response_openai = openai_run(claims , abstract , system_message , user_message ,  results)
        results.append(response_openai)

    print(results)

    df_result = pd.DataFrame(results)
    df_result.insert(0 , "Patent Number" , df[0])
    df_result.to_excel("results.xlsx" , index=False)





def openai_run(claims , abstract ,system_message ,  user_message , results):
    user_message += f"""\n\n
    Patent Abstract: {abstract}\n
    Patent Claims: {claims}

    Extract taxonomy-related text exclusively from the abstract and claims, with no additional explanation.
    """
    print(MODEL)
    try:
        completion = client.chat.completions.create(
        model=MODEL,
        messages=[
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_message}
        ]
        )

        print("Assistant: " + completion.choices[0].message.content)
        response = completion.choices[0].message.content
        response_json = parser.parse(response)
    except Exception as e:
        logging.warning(f"Ran into LLM related Issue {e}")

    
    # active_material_data = {
    #         "negative_electrode": response_json.get("negative_electrode_active_material", ""),
    #         "positive_electrode": response_json.get("positive_electrode_active_material", ""),
    #         "electrodes_llm_comments": response_json.get("llm_comments", "")
    #     }

    response_openai = {}
    for key in list(response_json.keys()):
        response_openai[key] = response_json.get(key , "")

    return response_openai

@app.get("/get_data_taxonomy")
async def get_data_taxonomy():
    file_path = "results.xlsx"
    if os.path.exists(file_path):
        df = pd.read_excel(file_path)
        # Replace JSON-invalid values with None
        df.replace([np.nan, np.inf, -np.inf], None, inplace=True)

        # Convert DataFrame to list of records (rows)
        records = df.to_dict(orient="records")

        # Safe serialize (ensures JSON-compliance)
        safe_data = json.loads(json.dumps(records))
        try:

            # In FastAPI
            return JSONResponse(content={"status": "completed", "message": safe_data})
            # df = df.where(pd.notnull(df), None)
            # data  = df.to_dict(orient="records")
            # return {"status" : "completed" , "message" : data}
        except Exception as e:
            return JSONResponse(status_code = 500 ,
                content = {"status" : "failed" , "message" : f"{e}"}
                )
    
    else:
        return JSONResponse(
            status_code = 202 , 
            content={"status": "processing", "message": "Data is still being processed."}
        )




