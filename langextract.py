"""
LangExtract-based Patent Text Extraction System
This module implements domain-independent patent text extraction using Google's LangExtract library.
"""

import requests
import pandas as pd
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import json
from langextract import ExtractorConfig, Extractor
from langextract.tasks import Text, EntityNode
from langextract.llm import LLMIntegration, GeminiLLM, OpenAILLM


@dataclass
class PatentData:
    """Structure to hold patent information"""
    patent_number: str
    abstract: str
    claims: str
    description: str = ""
    title: str = ""


@dataclass
class TaxonomyResult:
    """Structure to hold extraction results for a taxonomy"""
    taxonomy: str
    extracted_chunks: List[str]
    confidence_scores: List[float]
    source_spans: List[tuple]  # (start_char, end_char) positions
    context: str  # Additional context information


class PatentLangExtractProcessor:
    """
    Main class for processing patents using LangExtract
    """

    def __init__(self,
                 llm_provider: str = "gemini",
                 api_key: str = "",
                 model_name: str = "gemini-pro",
                 chunk_size: int = 4000,
                 overlap_size: int = 200):
        """
        Initialize the patent processor

        Args:
            llm_provider: Either "gemini", "openai", or "local"
            api_key: API key for the LLM provider
            model_name: Name of the model to use
            chunk_size: Size of text chunks for processing
            overlap_size: Overlap between chunks
        """
        self.chunk_size = chunk_size
        self.overlap_size = overlap_size
        self.api_key = api_key

        # Initialize LLM based on provider
        if llm_provider == "gemini":
            self.llm = GeminiLLM(api_key=api_key, model_name=model_name)
        elif llm_provider == "openai":
            self.llm = OpenAILLM(api_key=api_key, model_name=model_name)
        else:
            raise ValueError(f"Unsupported LLM provider: {llm_provider}")

        self.extractor_configs = {}

    def fetch_patent_data(self, patent_number: str) -> PatentData:
        """
        Fetch patent data from Patent WR API

        Args:
            patent_number: Patent number to fetch

        Returns:
            PatentData object containing patent information
        """
        try:
            api_url = f"https://api.patent.wissenresearch.com/patent/detailed/{patent_number}"
            response = requests.get(api_url)
            response.raise_for_status()

            data = response.json()

            return PatentData(
                patent_number=patent_number,
                abstract=data.get('abstract', ''),
                claims=data.get('claims', ''),
                description=data.get('description', ''),
                title=data.get('title', '')
            )

        except requests.RequestException as e:
            raise Exception(f"Failed to fetch patent data for {patent_number}: {str(e)}")

    def create_taxonomy_extractor(self, taxonomy: str, examples: List[Dict] = None) -> None:
        """
        Create an extractor configuration for a specific taxonomy

        Args:
            taxonomy: The taxonomy category name
            examples: Few-shot examples for the taxonomy
        """
        # Default examples if none provided
        if examples is None:
            examples = self._get_default_examples(taxonomy)

        # Create the extraction schema
        schema = {
            "type": "object",
            "properties": {
                "relevant_chunks": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "text": {"type": "string", "description": "Extracted text chunk relevant to the taxonomy"},
                            "relevance": {"type": "string", "description": "Explanation of why this text is relevant"},
                            "confidence": {"type": "number", "description": "Confidence score 0-1"},
                            "technical_terms": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Key technical terms found in this chunk"
                            }
                        },
                        "required": ["text", "relevance", "confidence"]
                    }
                },
                "summary": {
                    "type": "string",
                    "description": "Brief summary of findings related to this taxonomy"
                }
            },
            "required": ["relevant_chunks", "summary"]
        }

        # Create extraction instructions
        instructions = f"""
        You are an expert in patent analysis. Your task is to identify and extract text segments 
        from patent documents that are relevant to the taxonomy: "{taxonomy}".

        Guidelines:
        1. Look for text that directly mentions, describes, or relates to {taxonomy}
        2. Include context around relevant mentions (full sentences/paragraphs)
        3. Extract multiple chunks if the taxonomy appears in different sections
        4. Focus on technical descriptions, implementations, and specific details
        5. Avoid generic or irrelevant text
        6. Provide confidence scores based on relevance strength
        7. Extract key technical terms that relate to the taxonomy

        Return all relevant text chunks with their context and explanations.
        """

        # Create extractor config
        config = ExtractorConfig(
            name=f"taxonomy_{taxonomy.lower().replace(' ', '_')}",
            instructions=instructions,
            schema=schema,
            examples=examples,
            llm_integration=LLMIntegration(self.llm)
        )

        self.extractor_configs[taxonomy] = config

    def _get_default_examples(self, taxonomy: str) -> List[Dict]:
        """
        Generate default few-shot examples based on taxonomy

        Args:
            taxonomy: The taxonomy category

        Returns:
            List of example dictionaries
        """
        # This would be expanded with domain-specific examples
        examples = [
            {
                "input": "Sample patent text with technical content...",
                "output": {
                    "relevant_chunks": [
                        {
                            "text": "Example relevant text chunk",
                            "relevance": "This text describes the specific implementation",
                            "confidence": 0.9,
                            "technical_terms": ["term1", "term2"]
                        }
                    ],
                    "summary": "Brief summary of findings"
                }
            }
        ]
        return examples

    def chunk_text(self, text: str) -> List[str]:
        """
        Split text into overlapping chunks for processing

        Args:
            text: Text to chunk

        Returns:
            List of text chunks
        """
        if len(text) <= self.chunk_size:
            return [text]

        chunks = []
        start = 0

        while start < len(text):
            end = min(start + self.chunk_size, len(text))

            # Try to break at sentence boundaries
            if end < len(text):
                # Look for sentence endings within the last 200 characters
                last_period = text.rfind('.', start + self.chunk_size - 200, end)
                if last_period > start:
                    end = last_period + 1

            chunks.append(text[start:end].strip())

            if end >= len(text):
                break

            start = end - self.overlap_size

        return chunks

    def extract_for_taxonomy(self, patent_data: PatentData, taxonomy: str) -> TaxonomyResult:
        """
        Extract relevant text for a specific taxonomy from patent data

        Args:
            patent_data: Patent information
            taxonomy: Taxonomy to extract for

        Returns:
            TaxonomyResult with extracted information
        """
        if taxonomy not in self.extractor_configs:
            self.create_taxonomy_extractor(taxonomy)

        # Combine patent text sections
        combined_text = f"""
        Title: {patent_data.title}

        Abstract: {patent_data.abstract}

        Claims: {patent_data.claims}

        Description: {patent_data.description}
        """.strip()

        # Split into chunks
        text_chunks = self.chunk_text(combined_text)

        # Create extractor
        extractor = Extractor(self.extractor_configs[taxonomy])

        all_extracted_chunks = []
        all_confidence_scores = []
        all_source_spans = []

        for chunk_idx, chunk in enumerate(text_chunks):
            try:
                # Create Text object for LangExtract
                text_obj = Text(chunk)

                # Extract information
                result = extractor.extract(text_obj)

                if result and 'relevant_chunks' in result:
                    for item in result['relevant_chunks']:
                        all_extracted_chunks.append(item.get('text', ''))
                        all_confidence_scores.append(item.get('confidence', 0.5))

                        # Calculate approximate source spans
                        text_start = chunk.find(item.get('text', ''))
                        if text_start >= 0:
                            chunk_start = sum(len(text_chunks[i]) for i in range(chunk_idx))
                            all_source_spans.append((
                                chunk_start + text_start,
                                chunk_start + text_start + len(item.get('text', ''))
                            ))
                        else:
                            all_source_spans.append((0, 0))

            except Exception as e:
                print(f"Error processing chunk {chunk_idx} for taxonomy {taxonomy}: {str(e)}")
                continue

        return TaxonomyResult(
            taxonomy=taxonomy,
            extracted_chunks=all_extracted_chunks,
            confidence_scores=all_confidence_scores,
            source_spans=all_source_spans,
            context=f"Processed {len(text_chunks)} chunks from patent {patent_data.patent_number}"
        )

    def process_patent_with_taxonomies(self,
                                       patent_number: str,
                                       taxonomies: List[str],
                                       min_confidence: float = 0.6) -> Dict[str, TaxonomyResult]:
        """
        Process a patent for multiple taxonomies

        Args:
            patent_number: Patent number to process
            taxonomies: List of taxonomy categories
            min_confidence: Minimum confidence threshold for results

        Returns:
            Dictionary mapping taxonomies to their results
        """
        # Fetch patent data
        patent_data = self.fetch_patent_data(patent_number)

        results = {}

        for taxonomy in taxonomies:
            try:
                result = self.extract_for_taxonomy(patent_data, taxonomy)

                # Filter by confidence threshold
                if min_confidence > 0:
                    filtered_chunks = []
                    filtered_scores = []
                    filtered_spans = []

                    for i, score in enumerate(result.confidence_scores):
                        if score >= min_confidence:
                            filtered_chunks.append(result.extracted_chunks[i])
                            filtered_scores.append(score)
                            filtered_spans.append(result.source_spans[i])

                    result.extracted_chunks = filtered_chunks
                    result.confidence_scores = filtered_scores
                    result.source_spans = filtered_spans

                results[taxonomy] = result

            except Exception as e:
                print(f"Error processing taxonomy {taxonomy}: {str(e)}")
                results[taxonomy] = TaxonomyResult(
                    taxonomy=taxonomy,
                    extracted_chunks=[],
                    confidence_scores=[],
                    source_spans=[],
                    context=f"Error: {str(e)}"
                )

        return results

    def export_results_to_dataframe(self,
                                    results: Dict[str, Dict[str, TaxonomyResult]]) -> pd.DataFrame:
        """
        Export results to a pandas DataFrame

        Args:
            results: Dictionary of patent_number -> taxonomy -> TaxonomyResult

        Returns:
            DataFrame with structured results
        """
        rows = []

        for patent_number, patent_results in results.items():
            for taxonomy, result in patent_results.items():
                if result.extracted_chunks:
                    # Create a row for each extracted chunk
                    for i, chunk in enumerate(result.extracted_chunks):
                        row = {
                            'patent_number': patent_number,
                            'taxonomy': taxonomy,
                            'extracted_text': chunk,
                            'confidence_score': result.confidence_scores[i] if i < len(
                                result.confidence_scores) else 0.0,
                            'source_span_start': result.source_spans[i][0] if i < len(result.source_spans) else 0,
                            'source_span_end': result.source_spans[i][1] if i < len(result.source_spans) else 0,
                            'context': result.context,
                            'chunk_length': len(chunk)
                        }
                        rows.append(row)
                else:
                    # Create a row indicating no results found
                    row = {
                        'patent_number': patent_number,
                        'taxonomy': taxonomy,
                        'extracted_text': '',
                        'confidence_score': 0.0,
                        'source_span_start': 0,
                        'source_span_end': 0,
                        'context': result.context,
                        'chunk_length': 0
                    }
                    rows.append(row)

        return pd.DataFrame(rows)

    def generate_visualization_report(self,
                                      results: Dict[str, TaxonomyResult],
                                      patent_data: PatentData,
                                      output_path: str = "patent_extraction_report.html"):
        """
        Generate an interactive HTML report showing extraction results

        Args:
            results: Extraction results for a single patent
            patent_data: Patent data
            output_path: Path to save HTML report
        """
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Patent Extraction Report - {patent_data.patent_number}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .taxonomy-section {{ margin: 20px 0; padding: 15px; border: 1px solid #ccc; }}
                .chunk {{ margin: 10px 0; padding: 10px; background-color: #f9f9f9; }}
                .confidence {{ font-weight: bold; color: green; }}
                .low-confidence {{ color: orange; }}
                .no-results {{ color: red; }}
            </style>
        </head>
        <body>
            <h1>Patent Extraction Report</h1>
            <h2>Patent: {patent_data.patent_number}</h2>
            <h3>Title: {patent_data.title}</h3>

            <div class="abstract">
                <h4>Abstract:</h4>
                <p>{patent_data.abstract}</p>
            </div>
        """

        for taxonomy, result in results.items():
            html_content += f"""
            <div class="taxonomy-section">
                <h4>Taxonomy: {taxonomy}</h4>
                <p><strong>Context:</strong> {result.context}</p>
            """

            if result.extracted_chunks:
                for i, chunk in enumerate(result.extracted_chunks):
                    confidence = result.confidence_scores[i] if i < len(result.confidence_scores) else 0.0
                    confidence_class = "confidence" if confidence >= 0.7 else "low-confidence"

                    html_content += f"""
                    <div class="chunk">
                        <p class="{confidence_class}">Confidence: {confidence:.2f}</p>
                        <p><strong>Extracted Text:</strong></p>
                        <p>{chunk}</p>
                    </div>
                    """
            else:
                html_content += '<p class="no-results">No relevant text found for this taxonomy.</p>'

            html_content += "</div>"

        html_content += """
        </body>
        </html>
        """

        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"Visualization report saved to: {output_path}")


def main_example():
    """
    Example usage of the PatentLangExtractProcessor
    """
    # Initialize processor
    processor = PatentLangExtractProcessor(
        llm_provider="gemini",  # or "openai"
        api_key="your-api-key-here",
        model_name="gemini-pro"
    )

    # Define taxonomies for analysis
    taxonomies = [
        "Genome Recoding",
        "Synthetic organism/ genomically recoded organisms (GRO)",
        "Non-canonical amino acids (ncAA)",
        "Orthogonal translation system (OTS)"
    ]

    # Process patents
    patent_numbers = ["US11408007B2", "US11649450B2", "US11788111B2"]
    all_results = {}

    for patent_number in patent_numbers:
        try:
            results = processor.process_patent_with_taxonomies(
                patent_number=patent_number,
                taxonomies=taxonomies,
                min_confidence=0.6
            )
            all_results[patent_number] = results

            # Generate individual report
            patent_data = processor.fetch_patent_data(patent_number)
            processor.generate_visualization_report(
                results=results,
                patent_data=patent_data,
                output_path=f"report_{patent_number}.html"
            )

        except Exception as e:
            print(f"Error processing patent {patent_number}: {str(e)}")

    # Export to DataFrame
    df = processor.export_results_to_dataframe(all_results)
    df.to_csv("langextract_patent_results.csv", index=False)

    print("Processing complete!")
    print(f"Results saved to: langextract_patent_results.csv")
    print(f"Total rows in results: {len(df)}")


if __name__ == "__main__":
    main_example()