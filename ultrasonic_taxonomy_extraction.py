#!/usr/bin/env python3
"""
Ultrasonic Sensor Taxonomy Extraction Script
Extracts specific taxonomies from ultrasonic sensor patents based on the provided data structure.
"""

import os
import sys
import logging
import pandas as pd
from typing import List, Dict, Any

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from patent_langextract import (
    PatentLangExtractProcessor,
    extract_taxonomies_from_patent,
    extract_taxonomies_from_text
)
from config import Config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Define ultrasonic sensor taxonomies based on your data structure
ULTRASONIC_TAXONOMIES = [
    # Transducer Types
    "Piezoelectric",
    "MEMS",
    "CMUT",
    "pMUT",
    
    # Signal Processing
    "Digital Signal Processing",
    "Time-of-Flight",
    "Neural Networks",
    
    # Frequency Ranges
    "Low Frequency Range",
    "Mid Frequency Range", 
    "High Frequency Range",
    
    # Automotive Applications
    "Parking Systems",
    "ADAS",
    "Blind Spot Detection",
    "Collision Detection",
    
    # Components and Materials
    "Membrane Design",
    "PZT Material",
    "Housing and Mounting",
    
    # Quality and Processing
    "Noise Reduction",
    "Quality Management",
    
    # Detection and Measurement
    "Object Detection",
    "Distance Measurement",
    "Detection Range"
]

def extract_ultrasonic_taxonomies(patent_numbers: List[str], 
                                 selected_taxonomies: List[str] = None,
                                 output_file: str = "ultrasonic_extraction_results.csv") -> pd.DataFrame:
    """
    Extract ultrasonic sensor taxonomies from multiple patents
    
    Args:
        patent_numbers: List of patent numbers to process
        selected_taxonomies: Specific taxonomies to extract (if None, uses all)
        output_file: Output CSV file name
        
    Returns:
        DataFrame with extraction results
    """
    if selected_taxonomies is None:
        selected_taxonomies = ULTRASONIC_TAXONOMIES
    
    print(f"🔍 Extracting {len(selected_taxonomies)} taxonomies from {len(patent_numbers)} patents")
    print(f"Taxonomies: {', '.join(selected_taxonomies[:5])}{'...' if len(selected_taxonomies) > 5 else ''}")
    
    # Initialize processor with optimized settings
    processor = PatentLangExtractProcessor(
        model_id="gemini-2.5-flash",  # Fast model for efficiency
        chunk_size=4000,  # Reasonable chunk size
        overlap_size=200
    )
    
    # Process patents in batch
    all_results = processor.extract_patent_taxonomies_batch(
        patent_numbers=patent_numbers,
        taxonomies=selected_taxonomies,
        min_confidence=0.6  # Reasonable confidence threshold
    )
    
    # Export to DataFrame
    df = processor.export_results_to_dataframe(all_results)
    
    # Create output directory
    os.makedirs(Config.OUTPUT_DIR, exist_ok=True)
    output_path = os.path.join(Config.OUTPUT_DIR, output_file)
    
    # Save results
    df.to_csv(output_path, index=False)
    print(f"💾 Results saved to: {output_path}")
    
    # Generate summary report
    generate_summary_report(df, all_results)
    
    return df

def generate_summary_report(df: pd.DataFrame, results: Dict[str, Dict[str, Any]]):
    """Generate a summary report of extraction results"""
    
    print("\n" + "="*60)
    print("📊 EXTRACTION SUMMARY REPORT")
    print("="*60)
    
    # Overall statistics
    total_patents = len(results)
    total_taxonomies = len(df['taxonomy'].unique())
    total_extractions = len(df[df['extracted_text'] != ''])
    
    print(f"📄 Patents processed: {total_patents}")
    print(f"🏷️  Taxonomies analyzed: {total_taxonomies}")
    print(f"✅ Successful extractions: {total_extractions}")
    print(f"📈 Success rate: {(total_extractions / (total_patents * total_taxonomies) * 100):.1f}%")
    
    # Top taxonomies by extraction count
    print(f"\n🔝 TOP TAXONOMIES BY EXTRACTION COUNT:")
    taxonomy_counts = df[df['extracted_text'] != ''].groupby('taxonomy').size().sort_values(ascending=False)
    for taxonomy, count in taxonomy_counts.head(10).items():
        print(f"   {taxonomy}: {count} extractions")
    
    # Patent-wise results
    print(f"\n📋 PATENT-WISE RESULTS:")
    for patent_number, patent_results in results.items():
        successful_extractions = sum(1 for result in patent_results.values() if result.extracted_chunks)
        total_taxonomies_for_patent = len(patent_results)
        success_rate = (successful_extractions / total_taxonomies_for_patent * 100) if total_taxonomies_for_patent > 0 else 0
        
        print(f"   {patent_number}: {successful_extractions}/{total_taxonomies_for_patent} taxonomies ({success_rate:.1f}%)")
    
    # Average confidence scores
    if not df[df['confidence_score'] > 0].empty:
        avg_confidence = df[df['confidence_score'] > 0]['confidence_score'].mean()
        print(f"\n🎯 Average confidence score: {avg_confidence:.2f}")
    
    # Generate detailed HTML report
    generate_html_report(df, results)

def generate_html_report(df: pd.DataFrame, results: Dict[str, Dict[str, Any]]):
    """Generate detailed HTML report"""
    
    os.makedirs(Config.REPORTS_DIR, exist_ok=True)
    report_path = os.path.join(Config.REPORTS_DIR, "ultrasonic_taxonomy_report.html")
    
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Ultrasonic Sensor Taxonomy Extraction Report</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { background-color: #f0f8ff; padding: 20px; border-radius: 5px; }
            .patent-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
            .taxonomy-result { margin: 10px 0; padding: 10px; background-color: #f9f9f9; }
            .high-confidence { border-left: 4px solid #4CAF50; }
            .medium-confidence { border-left: 4px solid #FF9800; }
            .low-confidence { border-left: 4px solid #f44336; }
            .no-result { border-left: 4px solid #ccc; color: #666; }
            .confidence-score { font-weight: bold; }
            .extracted-text { margin: 5px 0; font-style: italic; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🔊 Ultrasonic Sensor Taxonomy Extraction Report</h1>
            <p>Generated on: """ + pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S') + """</p>
        </div>
    """
    
    for patent_number, patent_results in results.items():
        html_content += f"""
        <div class="patent-section">
            <h2>📄 Patent: {patent_number}</h2>
        """
        
        for taxonomy, result in patent_results.items():
            if result.extracted_chunks:
                confidence_class = "high-confidence" if max(result.confidence_scores) >= 0.8 else \
                                 "medium-confidence" if max(result.confidence_scores) >= 0.6 else "low-confidence"
                
                html_content += f"""
                <div class="taxonomy-result {confidence_class}">
                    <h4>🏷️ {taxonomy}</h4>
                    <p class="confidence-score">Confidence: {max(result.confidence_scores):.2f}</p>
                """
                
                for i, chunk in enumerate(result.extracted_chunks):
                    conf = result.confidence_scores[i] if i < len(result.confidence_scores) else 0.0
                    html_content += f"""
                    <div class="extracted-text">
                        <strong>Extract {i+1} (Conf: {conf:.2f}):</strong><br>
                        {chunk[:300]}{'...' if len(chunk) > 300 else ''}
                    </div>
                    """
                
                html_content += "</div>"
            else:
                html_content += f"""
                <div class="taxonomy-result no-result">
                    <h4>🏷️ {taxonomy}</h4>
                    <p>No relevant text found</p>
                </div>
                """
        
        html_content += "</div>"
    
    html_content += """
    </body>
    </html>
    """
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"📊 Detailed HTML report saved to: {report_path}")

def main():
    """Main execution function"""
    print("🚀 Ultrasonic Sensor Taxonomy Extraction")
    print(f"Configuration:")
    print(f"  - Gemini API: {'✅ Configured' if Config.GEMINI_API_KEY else '❌ Missing'}")
    print(f"  - Patent API: {Config.PATENT_API_BASE_URL}")
    
    # Test patents from your data
    test_patents = [
        "US20250189647A1",  # Method for ultrasound-based object classification
        "WO2025131593A1",   # Ultrasonic sensor and method for producing
        "EP4607244A1",      # Additional test patent
        "US20250271565A1"   # Additional test patent
    ]
    
    # Select key taxonomies for initial testing
    key_taxonomies = [
        "Piezoelectric",
        "MEMS", 
        "Digital Signal Processing",
        "Parking Systems",
        "Object Detection",
        "Distance Measurement",
        "Noise Reduction",
        "PZT Material"
    ]
    
    try:
        # Extract taxonomies
        df = extract_ultrasonic_taxonomies(
            patent_numbers=test_patents,
            selected_taxonomies=key_taxonomies,
            output_file="ultrasonic_sensor_results.csv"
        )
        
        print(f"\n🎉 Extraction completed successfully!")
        print(f"📊 Total results: {len(df)} rows")
        print(f"✅ Successful extractions: {len(df[df['extracted_text'] != ''])}")
        
    except Exception as e:
        print(f"❌ Error during extraction: {str(e)}")
        logging.error(f"Extraction failed: {str(e)}")

if __name__ == "__main__":
    main()
