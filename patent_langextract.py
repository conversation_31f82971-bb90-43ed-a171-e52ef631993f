"""
LangExtract-based Patent Text Extraction System
This module implements domain-independent patent text extraction using Google's LangExtract library.
"""

import requests
import pandas as pd
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import json
import os
import logging
import langextract as lx
from config import Config


@dataclass
class PatentData:
    """Structure to hold patent information"""
    patent_number: str
    abstract: str
    claims: str
    description: str = ""
    title: str = ""


@dataclass
class TaxonomyResult:
    """Structure to hold extraction results for a taxonomy"""
    taxonomy: str
    extracted_chunks: List[str]
    confidence_scores: List[float]
    source_spans: List[tuple]  # (start_char, end_char) positions
    context: str  # Additional context information


class PatentLangExtractProcessor:
    """
    Main class for processing patents using LangExtract
    """

    def __init__(self,
                 model_id: Optional[str] = None,
                 api_key: Optional[str] = None,
                 chunk_size: Optional[int] = None,
                 overlap_size: Optional[int] = None):
        """
        Initialize the patent processor

        Args:
            model_id: Model ID to use (if None, uses config default)
            api_key: API key for the LLM provider (if None, uses environment variables)
            chunk_size: Size of text chunks for processing (if None, uses config default)
            overlap_size: Overlap between chunks (if None, uses config default)
        """
        # Set defaults from config
        if model_id is None:
            model_id = Config.DEFAULT_GEMINI_MODEL

        self.model_id = model_id
        self.chunk_size = chunk_size or Config.DEFAULT_CHUNK_SIZE
        self.overlap_size = overlap_size or Config.DEFAULT_OVERLAP

        # Set API key from config if not provided
        if api_key is None:
            if "gemini" in model_id.lower():
                self.api_key = Config.GEMINI_API_KEY
            elif "gpt" in model_id.lower():
                self.api_key = Config.OPENAI_API_KEY
            else:
                self.api_key = os.getenv('LANGEXTRACT_API_KEY')
        else:
            self.api_key = api_key

        # Validate API key
        if not self.api_key:
            logging.warning(f"No API key found for model {model_id}. Please check your environment variables.")

        # Set up logging
        logging.basicConfig(level=getattr(logging, Config.LOG_LEVEL.upper(), logging.INFO))

    def fetch_patent_data(self, patent_number: str) -> PatentData:
        """
        Fetch patent data from Patent WR API using existing patterns

        Args:
            patent_number: Patent number to fetch

        Returns:
            PatentData object containing patent information
        """
        try:
            # Construct API URL - format is https://api.patent.wissenresearch.com/patent/{patent_number}
            base_url = "https://api.patent.wissenresearch.com"
            api_url = f"{base_url}/patent/{patent_number}"
            logging.info(f"Fetching patent data from: {api_url}")

            response = requests.get(api_url, timeout=Config.REQUEST_TIMEOUT)
            response.raise_for_status()

            data = response.json()

            # Validate that we have the required fields
            if not data.get('abstract') and not data.get('claims'):
                logging.warning(f"Patent {patent_number} has no abstract or claims data")

            return PatentData(
                patent_number=patent_number,
                abstract=data.get('abstract', ''),
                claims=data.get('claims', ''),
                description=data.get('description', ''),
                title=data.get('title', '')
            )

        except requests.Timeout:
            logging.error(f"Timeout fetching patent data for {patent_number}")
            raise Exception(f"Timeout fetching patent data for {patent_number}")
        except requests.RequestException as e:
            logging.error(f"Request failed for patent {patent_number}: {str(e)}")
            raise Exception(f"Failed to fetch patent data for {patent_number}: {str(e)}")
        except json.JSONDecodeError as e:
            logging.error(f"Invalid JSON response for patent {patent_number}: {str(e)}")
            raise Exception(f"Invalid response format for patent {patent_number}: {str(e)}")
        except Exception as e:
            logging.error(f"Unexpected error fetching patent {patent_number}: {str(e)}")
            raise Exception(f"Unexpected error fetching patent data for {patent_number}: {str(e)}")

    def create_taxonomy_examples(self, taxonomy: str) -> List[lx.data.ExampleData]:
        """
        Create few-shot examples for a specific taxonomy

        Args:
            taxonomy: The taxonomy category name

        Returns:
            List of ExampleData objects for the taxonomy
        """
        # Create domain-specific examples based on taxonomy
        if "Piezo" in taxonomy.lower() or "Piezo electric" in taxonomy.lower():
            example_text = """[0033]For simulating a changed sensor arrangement, amplitude corrections and travel time corrections to the time signals may be necessary and the following influences by the sensor arrangement can be taken into account (in the training and/or adaptively during execution): the angle of attack (−25° to +25°) and the resulting change in amplitude due to the directional characteristic of the sensors; vertical or horizontal position of the sensors in the bumper and the resulting change in travel time and amplitude due to the changed distance to the obstacle and the ground; and/or geometry of the installation environment (smooth bumper/grille/mounting cone) and the resulting change in directional characteristic.
            [0034]According to an example embodiment of the present invention, ultrasonic time signals of at least one sensor can be used and taken as an analog signal directly at the output of the electrical amplification circuit after the piezo element. Furthermore, a high-resolution digital ultrasonic time signal can then be generated by means of an analog-to-digital converter according to a predetermined sampling theorem, for example according to Shannon, wherein the sampling can typically take place at >=100 kHz, preferably 200 kHz."""

            extractions = [
                lx.data.Extraction(
                    extraction_class="Piezo electric",
                    extraction_text="modified genetic code",
                    attributes={
                        "type": "genetic code modification",
                        "target": "bacterial cell",
                        "confidence": 0.9
                    }
                ),
                lx.data.Extraction(
                    extraction_class="recoding_technique",
                    extraction_text="reassignment of amber stop codons",
                    attributes={
                        "method": "codon reassignment",
                        "codon_type": "amber stop codon",
                        "confidence": 0.95
                    }
                ),
                lx.data.Extraction(
                    extraction_class="organism_type",
                    extraction_text="genomically recoded organism",
                    attributes={
                        "organism_category": "GRO",
                        "modification_level": "genomic",
                        "confidence": 0.98
                    }
                )
            ]
        else:
            # Generic example for other taxonomies
            example_text = f"""The invention relates to {taxonomy.lower()} technology,
            specifically involving novel methods and compositions for enhanced performance."""

            extractions = [
                lx.data.Extraction(
                    extraction_class="technology_reference",
                    extraction_text=f"{taxonomy.lower()} technology",
                    attributes={
                        "category": taxonomy,
                        "type": "direct_reference",
                        "confidence": 0.8
                    }
                ),
                lx.data.Extraction(
                    extraction_class="technical_method",
                    extraction_text="novel methods and compositions",
                    attributes={
                        "innovation_type": "method_and_composition",
                        "purpose": "enhanced performance",
                        "confidence": 0.7
                    }
                )
            ]

        return [lx.data.ExampleData(text=example_text, extractions=extractions)]

    def create_extraction_prompt(self, taxonomy: str) -> str:
        """
        Create extraction prompt for a specific taxonomy

        Args:
            taxonomy: The taxonomy category name

        Returns:
            Formatted prompt string
        """
        return f"""
        Extract text segments from patent documents that are relevant to the taxonomy: "{taxonomy}".

        Guidelines:
        1. Look for text that directly mentions, describes, or relates to {taxonomy}
        2. Include context around relevant mentions (full sentences/paragraphs)
        3. Extract multiple chunks if the taxonomy appears in different sections
        4. Focus on technical descriptions, implementations, and specific details
        5. Avoid generic or irrelevant text
        6. Provide confidence scores based on relevance strength
        7. Extract key technical terms that relate to the taxonomy

        Use exact text for extractions. Do not paraphrase or overlap entities.
        Provide meaningful attributes for each entity to add context.
        """

    def extract_for_taxonomy(self, patent_data: PatentData, taxonomy: str) -> TaxonomyResult:
        """
        Extract relevant text for a specific taxonomy from patent data using LangExtract

        Args:
            patent_data: Patent information
            taxonomy: Taxonomy to extract for

        Returns:
            TaxonomyResult with extracted information
        """
        # Combine patent text sections
        combined_text = f"""
        Title: {patent_data.title}

        Abstract: {patent_data.abstract}

        Claims: {patent_data.claims}

        Description: {patent_data.description}
        """.strip()

        # Create prompt and examples for this taxonomy
        prompt = self.create_extraction_prompt(taxonomy)
        examples = self.create_taxonomy_examples(taxonomy)

        try:
            # Use langextract to perform extraction
            result = lx.extract(
                text_or_documents=combined_text,
                prompt_description=prompt,
                examples=examples,
                model_id=self.model_id,
                api_key=self.api_key,
                max_char_buffer=self.chunk_size,
                extraction_passes=Config.EXTRACTION_PASSES,
                max_workers=Config.MAX_WORKERS
            )

            # Process the langextract result into our format
            extracted_chunks = []
            confidence_scores = []
            source_spans = []

            if hasattr(result, 'extractions') and result.extractions:
                for extraction in result.extractions:
                    extracted_chunks.append(extraction.extraction_text)

                    # Get confidence from attributes or default
                    confidence = extraction.attributes.get('confidence', 0.7) if extraction.attributes else 0.7
                    confidence_scores.append(float(confidence))

                    # Get source spans if available
                    if hasattr(extraction, 'char_interval') and extraction.char_interval:
                        source_spans.append((extraction.char_interval.start, extraction.char_interval.end))
                    else:
                        # Find approximate position in text
                        start_pos = combined_text.find(extraction.extraction_text)
                        if start_pos >= 0:
                            source_spans.append((start_pos, start_pos + len(extraction.extraction_text)))
                        else:
                            source_spans.append((0, 0))

            return TaxonomyResult(
                taxonomy=taxonomy,
                extracted_chunks=extracted_chunks,
                confidence_scores=confidence_scores,
                source_spans=source_spans,
                context=f"Processed patent {patent_data.patent_number} using LangExtract with {len(extracted_chunks)} extractions"
            )

        except Exception as e:
            print(f"Error processing taxonomy {taxonomy} for patent {patent_data.patent_number}: {str(e)}")
            return TaxonomyResult(
                taxonomy=taxonomy,
                extracted_chunks=[],
                confidence_scores=[],
                source_spans=[],
                context=f"Error: {str(e)}"
            )



    def process_patent_with_taxonomies(self,
                                       patent_number: str,
                                       taxonomies: List[str],
                                       min_confidence: float = None) -> Dict[str, TaxonomyResult]:
        """
        Process a patent for multiple taxonomies using LangExtract

        Args:
            patent_number: Patent number to process
            taxonomies: List of taxonomy categories
            min_confidence: Minimum confidence threshold for results (uses config default if None)

        Returns:
            Dictionary mapping taxonomies to their results
        """
        if min_confidence is None:
            min_confidence = Config.MIN_CONFIDENCE_THRESHOLD

        # Fetch patent data
        patent_data = self.fetch_patent_data(patent_number)

        results = {}

        for taxonomy in taxonomies:
            try:
                result = self.extract_for_taxonomy(patent_data, taxonomy)

                # Filter by confidence threshold
                if min_confidence > 0 and result.confidence_scores:
                    filtered_chunks = []
                    filtered_scores = []
                    filtered_spans = []

                    for i, score in enumerate(result.confidence_scores):
                        if score >= min_confidence:
                            if i < len(result.extracted_chunks):
                                filtered_chunks.append(result.extracted_chunks[i])
                            if i < len(result.source_spans):
                                filtered_spans.append(result.source_spans[i])
                            filtered_scores.append(score)

                    result.extracted_chunks = filtered_chunks
                    result.confidence_scores = filtered_scores
                    result.source_spans = filtered_spans

                results[taxonomy] = result

            except Exception as e:
                print(f"Error processing taxonomy {taxonomy}: {str(e)}")
                results[taxonomy] = TaxonomyResult(
                    taxonomy=taxonomy,
                    extracted_chunks=[],
                    confidence_scores=[],
                    source_spans=[],
                    context=f"Error: {str(e)}"
                )

        return results

    def extract_patent_taxonomies_batch(self,
                                       patent_numbers: List[str],
                                       taxonomies: List[str],
                                       min_confidence: float = None) -> Dict[str, Dict[str, TaxonomyResult]]:
        """
        Process multiple patents for multiple taxonomies in batch

        Args:
            patent_numbers: List of patent numbers to process
            taxonomies: List of taxonomy categories
            min_confidence: Minimum confidence threshold for results

        Returns:
            Dictionary mapping patent_number -> taxonomy -> TaxonomyResult
        """
        all_results = {}

        for patent_number in patent_numbers:
            try:
                logging.info(f"Processing patent: {patent_number}")
                results = self.process_patent_with_taxonomies(
                    patent_number=patent_number,
                    taxonomies=taxonomies,
                    min_confidence=min_confidence
                )
                all_results[patent_number] = results
                logging.info(f"Successfully processed patent: {patent_number}")

            except Exception as e:
                logging.error(f"Error processing patent {patent_number}: {str(e)}")
                # Create empty results for failed patents
                all_results[patent_number] = {
                    taxonomy: TaxonomyResult(
                        taxonomy=taxonomy,
                        extracted_chunks=[],
                        confidence_scores=[],
                        source_spans=[],
                        context=f"Error processing patent: {str(e)}"
                    ) for taxonomy in taxonomies
                }

        return all_results

    def export_results_to_dataframe(self,
                                    results: Dict[str, Dict[str, TaxonomyResult]]) -> pd.DataFrame:
        """
        Export results to a pandas DataFrame

        Args:
            results: Dictionary of patent_number -> taxonomy -> TaxonomyResult

        Returns:
            DataFrame with structured results
        """
        rows = []

        for patent_number, patent_results in results.items():
            for taxonomy, result in patent_results.items():
                if result.extracted_chunks:
                    # Create a row for each extracted chunk
                    for i, chunk in enumerate(result.extracted_chunks):
                        row = {
                            'patent_number': patent_number,
                            'taxonomy': taxonomy,
                            'extracted_text': chunk,
                            'confidence_score': result.confidence_scores[i] if i < len(
                                result.confidence_scores) else 0.0,
                            'source_span_start': result.source_spans[i][0] if i < len(result.source_spans) else 0,
                            'source_span_end': result.source_spans[i][1] if i < len(result.source_spans) else 0,
                            'context': result.context,
                            'chunk_length': len(chunk)
                        }
                        rows.append(row)
                else:
                    # Create a row indicating no results found
                    row = {
                        'patent_number': patent_number,
                        'taxonomy': taxonomy,
                        'extracted_text': '',
                        'confidence_score': 0.0,
                        'source_span_start': 0,
                        'source_span_end': 0,
                        'context': result.context,
                        'chunk_length': 0
                    }
                    rows.append(row)

        return pd.DataFrame(rows)

    def generate_visualization_report(self,
                                      results: Dict[str, TaxonomyResult],
                                      patent_data: PatentData,
                                      output_path: str = "patent_extraction_report.html"):
        """
        Generate an interactive HTML report showing extraction results

        Args:
            results: Extraction results for a single patent
            patent_data: Patent data
            output_path: Path to save HTML report
        """
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Patent Extraction Report - {patent_data.patent_number}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .taxonomy-section {{ margin: 20px 0; padding: 15px; border: 1px solid #ccc; }}
                .chunk {{ margin: 10px 0; padding: 10px; background-color: #f9f9f9; }}
                .confidence {{ font-weight: bold; color: green; }}
                .low-confidence {{ color: orange; }}
                .no-results {{ color: red; }}
            </style>
        </head>
        <body>
            <h1>Patent Extraction Report</h1>
            <h2>Patent: {patent_data.patent_number}</h2>
            <h3>Title: {patent_data.title}</h3>

            <div class="abstract">
                <h4>Abstract:</h4>
                <p>{patent_data.abstract}</p>
            </div>
        """

        for taxonomy, result in results.items():
            html_content += f"""
            <div class="taxonomy-section">
                <h4>Taxonomy: {taxonomy}</h4>
                <p><strong>Context:</strong> {result.context}</p>
            """

            if result.extracted_chunks:
                for i, chunk in enumerate(result.extracted_chunks):
                    confidence = result.confidence_scores[i] if i < len(result.confidence_scores) else 0.0
                    confidence_class = "confidence" if confidence >= 0.7 else "low-confidence"

                    html_content += f"""
                    <div class="chunk">
                        <p class="{confidence_class}">Confidence: {confidence:.2f}</p>
                        <p><strong>Extracted Text:</strong></p>
                        <p>{chunk}</p>
                    </div>
                    """
            else:
                html_content += '<p class="no-results">No relevant text found for this taxonomy.</p>'

            html_content += "</div>"

        html_content += """
        </body>
        </html>
        """

        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"Visualization report saved to: {output_path}")


# Convenience functions that match existing extraction_methods.py patterns
def extract_taxonomies_from_patent(patent_number: str,
                                  taxonomies: List[str],
                                  model_id: str = "gemini-2.5-flash",
                                  min_confidence: float = None) -> Dict[str, Any]:
    """
    Extract taxonomies from a single patent - convenience function matching existing patterns

    Args:
        patent_number: Patent number to process
        taxonomies: List of taxonomy categories to extract
        model_id: Model to use for extraction
        min_confidence: Minimum confidence threshold

    Returns:
        Dictionary with extraction results in a format similar to existing extraction methods
    """
    processor = PatentLangExtractProcessor(model_id=model_id)

    try:
        results = processor.process_patent_with_taxonomies(
            patent_number=patent_number,
            taxonomies=taxonomies,
            min_confidence=min_confidence
        )

        # Convert to flat dictionary format similar to existing extraction methods
        flat_results = {"patent_number": patent_number}

        for taxonomy, result in results.items():
            taxonomy_key = taxonomy.lower().replace(' ', '_').replace('/', '_')

            if result.extracted_chunks:
                # Join multiple chunks with separator
                flat_results[f"{taxonomy_key}_text"] = " | ".join(result.extracted_chunks)
                flat_results[f"{taxonomy_key}_confidence"] = max(result.confidence_scores) if result.confidence_scores else 0.0
                flat_results[f"{taxonomy_key}_count"] = len(result.extracted_chunks)
            else:
                flat_results[f"{taxonomy_key}_text"] = ""
                flat_results[f"{taxonomy_key}_confidence"] = 0.0
                flat_results[f"{taxonomy_key}_count"] = 0

            flat_results[f"{taxonomy_key}_context"] = result.context

        return flat_results

    except Exception as e:
        logging.error(f"Error in extract_taxonomies_from_patent for {patent_number}: {str(e)}")
        # Return error results
        flat_results = {"patent_number": patent_number, "error": str(e)}
        for taxonomy in taxonomies:
            taxonomy_key = taxonomy.lower().replace(' ', '_').replace('/', '_')
            flat_results[f"{taxonomy_key}_text"] = ""
            flat_results[f"{taxonomy_key}_confidence"] = 0.0
            flat_results[f"{taxonomy_key}_count"] = 0
            flat_results[f"{taxonomy_key}_context"] = f"Error: {str(e)}"

        return flat_results


def extract_taxonomies_from_text(abstract: str,
                                claims: str,
                                taxonomies: List[str],
                                model_id: str = "gemini-2.5-flash",
                                patent_number: str = "UNKNOWN") -> Dict[str, Any]:
    """
    Extract taxonomies directly from abstract and claims text - matches existing extraction pattern

    Args:
        abstract: Patent abstract text
        claims: Patent claims text
        taxonomies: List of taxonomy categories to extract
        model_id: Model to use for extraction
        patent_number: Patent number for reference

    Returns:
        Dictionary with extraction results
    """
    # Create a temporary PatentData object
    patent_data = PatentData(
        patent_number=patent_number,
        abstract=abstract,
        claims=claims,
        description="",
        title=""
    )

    processor = PatentLangExtractProcessor(model_id=model_id)

    try:
        flat_results = {"patent_number": patent_number}

        for taxonomy in taxonomies:
            result = processor.extract_for_taxonomy(patent_data, taxonomy)
            taxonomy_key = taxonomy.lower().replace(' ', '_').replace('/', '_')

            if result.extracted_chunks:
                flat_results[f"{taxonomy_key}_text"] = " | ".join(result.extracted_chunks)
                flat_results[f"{taxonomy_key}_confidence"] = max(result.confidence_scores) if result.confidence_scores else 0.0
                flat_results[f"{taxonomy_key}_count"] = len(result.extracted_chunks)
            else:
                flat_results[f"{taxonomy_key}_text"] = ""
                flat_results[f"{taxonomy_key}_confidence"] = 0.0
                flat_results[f"{taxonomy_key}_count"] = 0

            flat_results[f"{taxonomy_key}_context"] = result.context

        return flat_results

    except Exception as e:
        logging.error(f"Error in extract_taxonomies_from_text for {patent_number}: {str(e)}")
        flat_results = {"patent_number": patent_number, "error": str(e)}
        for taxonomy in taxonomies:
            taxonomy_key = taxonomy.lower().replace(' ', '_').replace('/', '_')
            flat_results[f"{taxonomy_key}_text"] = ""
            flat_results[f"{taxonomy_key}_confidence"] = 0.0
            flat_results[f"{taxonomy_key}_count"] = 0
            flat_results[f"{taxonomy_key}_context"] = f"Error: {str(e)}"

        return flat_results

def export_results_to_dataframe(self,
                                results: Dict[str, Dict[str, TaxonomyResult]]) -> pd.DataFrame:
    """
    Export results to a pandas DataFrame

    Args:
        results: Dictionary of patent_number -> taxonomy -> TaxonomyResult

    Returns:
        DataFrame with structured results
    """
    rows = []

    for patent_number, patent_results in results.items():
        for taxonomy, result in patent_results.items():
            if result.extracted_chunks:
                # Create a row for each extracted chunk
                for i, chunk in enumerate(result.extracted_chunks):
                    row = {
                        'patent_number': patent_number,
                        'taxonomy': taxonomy,
                        'extracted_text': chunk,
                        'confidence_score': result.confidence_scores[i] if i < len(
                            result.confidence_scores) else 0.0,
                        'source_span_start': result.source_spans[i][0] if i < len(result.source_spans) else 0,
                        'source_span_end': result.source_spans[i][1] if i < len(result.source_spans) else 0,
                        'context': result.context,
                        'chunk_length': len(chunk)
                    }
                    rows.append(row)
            else:
                # Create a row indicating no results found
                row = {
                    'patent_number': patent_number,
                    'taxonomy': taxonomy,
                    'extracted_text': '',
                    'confidence_score': 0.0,
                    'source_span_start': 0,
                    'source_span_end': 0,
                    'context': result.context,
                    'chunk_length': 0
                }
                rows.append(row)

    return pd.DataFrame(rows)

def generate_visualization_report(self,
                                  results: Dict[str, TaxonomyResult],
                                  patent_data: PatentData,
                                  output_path: str = "patent_extraction_report.html"):
    """
    Generate an interactive HTML report showing extraction results

    Args:
        results: Extraction results for a single patent
        patent_data: Patent data
        output_path: Path to save HTML report
    """
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Patent Extraction Report - {patent_data.patent_number}</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .taxonomy-section {{ margin: 20px 0; padding: 15px; border: 1px solid #ccc; }}
            .chunk {{ margin: 10px 0; padding: 10px; background-color: #f9f9f9; }}
            .confidence {{ font-weight: bold; color: green; }}
            .low-confidence {{ color: orange; }}
            .no-results {{ color: red; }}
        </style>
    </head>
    <body>
        <h1>Patent Extraction Report</h1>
        <h2>Patent: {patent_data.patent_number}</h2>
        <h3>Title: {patent_data.title}</h3>

        <div class="abstract">
            <h4>Abstract:</h4>
            <p>{patent_data.abstract}</p>
        </div>
    """

    for taxonomy, result in results.items():
        html_content += f"""
        <div class="taxonomy-section">
            <h4>Taxonomy: {taxonomy}</h4>
            <p><strong>Context:</strong> {result.context}</p>
        """

        if result.extracted_chunks:
            for i, chunk in enumerate(result.extracted_chunks):
                confidence = result.confidence_scores[i] if i < len(result.confidence_scores) else 0.0
                confidence_class = "confidence" if confidence >= 0.7 else "low-confidence"

                html_content += f"""
                <div class="chunk">
                    <p class="{confidence_class}">Confidence: {confidence:.2f}</p>
                    <p><strong>Extracted Text:</strong></p>
                    <p>{chunk}</p>
                </div>
                """
        else:
            html_content += '<p class="no-results">No relevant text found for this taxonomy.</p>'

        html_content += "</div>"

    html_content += """
    </body>
    </html>
    """

    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)

    print(f"Visualization report saved to: {output_path}")


def main_example():
    """
    Example usage of the PatentLangExtractProcessor
    """
    # Initialize processor with configuration
    processor = PatentLangExtractProcessor(
        model_id="gemini-2.5-flash",  # Uses Config.GEMINI_API_KEY automatically
        chunk_size=Config.DEFAULT_CHUNK_SIZE,
        overlap_size=Config.DEFAULT_OVERLAP
    )

    # Define taxonomies for analysis
    taxonomies = [
        "Piezo electric",
        "Capacitive micromachined ultrasonic transducers (CMUTs)",
        "MEMS (micro-electromechanical systems)",
        "Piezoelectric micromachined ultrasonic transducer",
        "MPTs (Magnetostrictive Patch Transducers)",
        "EMATs (Electromagnetic Acoustic Transducers)",
        "Membrane thickness, shape, curvature",
        "Edge support/mounting structure",
        "PZT",
        "Metal",
        "Plastic",
        "Acoustic impedance matching/barrier layers",
        "Resonant frequency",
        "Unit",
        "Digital signal processor (DSP)",
        "Time-of-flight (ToF)",
        "Quiescent Current",
        "Effective Angle",
        "Sensitivity",
        "Other"
    ]

    # Process patents
    patent_numbers = ["EP4607244A1", "US20250271565A1"]
    all_results = {}

    # Create output directories
    os.makedirs(Config.OUTPUT_DIR, exist_ok=True)
    os.makedirs(Config.REPORTS_DIR, exist_ok=True)

    for patent_number in patent_numbers:
        try:
            print(f"Processing patent: {patent_number}")
            results = processor.process_patent_with_taxonomies(
                patent_number=patent_number,
                taxonomies=taxonomies
            )
            all_results[patent_number] = results

            # Generate individual report
            patent_data = processor.fetch_patent_data(patent_number)
            report_path = os.path.join(Config.REPORTS_DIR, f"report_{patent_number}.html")
            processor.generate_visualization_report(
                results=results,
                patent_data=patent_data,
                output_path=report_path
            )

            print(f"  - Generated report: {report_path}")

        except Exception as e:
            print(f"Error processing patent {patent_number}: {str(e)}")

    # Export to DataFrame
    df = processor.export_results_to_dataframe(all_results)
    output_path = os.path.join(Config.OUTPUT_DIR, "langextract_patent_results.csv")
    df.to_csv(output_path, index=False)

    print("Processing complete!")
    print(f"Results saved to: {output_path}")
    print(f"Total rows in results: {len(df)}")
    print(f"Reports saved to: {Config.REPORTS_DIR}")


if __name__ == "__main__":
    main_example()