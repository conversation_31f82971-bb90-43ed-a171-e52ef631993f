"""
LangExtract-based Patent Text Extraction System
This module implements domain-independent patent text extraction using Google's LangExtract library.
"""

import requests
import pandas as pd
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import json
import os
import langextract as lx
from config import Config


@dataclass
class PatentData:
    """Structure to hold patent information"""
    patent_number: str
    abstract: str
    claims: str
    description: str = ""
    title: str = ""


@dataclass
class TaxonomyResult:
    """Structure to hold extraction results for a taxonomy"""
    taxonomy: str
    extracted_chunks: List[str]
    confidence_scores: List[float]
    source_spans: List[tuple]  # (start_char, end_char) positions
    context: str  # Additional context information


class PatentLangExtractProcessor:
    """
    Main class for processing patents using LangExtract
    """

    def __init__(self,
                 model_id: str = "gemini-2.5-flash",
                 api_key: Optional[str] = None,
                 chunk_size: int = 4000,
                 overlap_size: int = 200):
        """
        Initialize the patent processor

        Args:
            model_id: Model ID to use (e.g., "gemini-2.5-flash", "gpt-4o")
            api_key: API key for the LLM provider (if None, uses environment variables)
            chunk_size: Size of text chunks for processing
            overlap_size: Overlap between chunks
        """
        self.model_id = model_id
        self.chunk_size = chunk_size
        self.overlap_size = overlap_size

        # Set API key from config if not provided
        if api_key is None:
            if "gemini" in model_id.lower():
                self.api_key = Config.GEMINI_API_KEY
            elif "gpt" in model_id.lower():
                self.api_key = Config.OPENAI_API_KEY
            else:
                self.api_key = os.getenv('LANGEXTRACT_API_KEY')
        else:
            self.api_key = api_key

    def fetch_patent_data(self, patent_number: str) -> PatentData:
        """
        Fetch patent data from Patent WR API

        Args:
            patent_number: Patent number to fetch

        Returns:
            PatentData object containing patent information
        """
        try:
            api_url = f"{Config.PATENT_API_BASE_URL}/patent/detailed/{patent_number}"
            response = requests.get(api_url)
            response.raise_for_status()

            data = response.json()

            return PatentData(
                patent_number=patent_number,
                abstract=data.get('abstract', ''),
                claims=data.get('claims', ''),
                description=data.get('description', ''),
                title=data.get('title', '')
            )

        except requests.RequestException as e:
            raise Exception(f"Failed to fetch patent data for {patent_number}: {str(e)}")

    def create_taxonomy_examples(self, taxonomy: str) -> List[lx.data.ExampleData]:
        """
        Create few-shot examples for a specific taxonomy

        Args:
            taxonomy: The taxonomy category name

        Returns:
            List of ExampleData objects for the taxonomy
        """
        # Create domain-specific examples based on taxonomy
        if "genome" in taxonomy.lower() or "genomic" in taxonomy.lower():
            example_text = """The method comprises introducing a modified genetic code into a bacterial cell,
            wherein the modified genetic code includes reassignment of amber stop codons to encode
            non-canonical amino acids, thereby creating a genomically recoded organism."""

            extractions = [
                lx.data.Extraction(
                    extraction_class="genomic_modification",
                    extraction_text="modified genetic code",
                    attributes={
                        "type": "genetic code modification",
                        "target": "bacterial cell",
                        "confidence": 0.9
                    }
                ),
                lx.data.Extraction(
                    extraction_class="recoding_technique",
                    extraction_text="reassignment of amber stop codons",
                    attributes={
                        "method": "codon reassignment",
                        "codon_type": "amber stop codon",
                        "confidence": 0.95
                    }
                ),
                lx.data.Extraction(
                    extraction_class="organism_type",
                    extraction_text="genomically recoded organism",
                    attributes={
                        "organism_category": "GRO",
                        "modification_level": "genomic",
                        "confidence": 0.98
                    }
                )
            ]
        else:
            # Generic example for other taxonomies
            example_text = f"""The invention relates to {taxonomy.lower()} technology,
            specifically involving novel methods and compositions for enhanced performance."""

            extractions = [
                lx.data.Extraction(
                    extraction_class="technology_reference",
                    extraction_text=f"{taxonomy.lower()} technology",
                    attributes={
                        "category": taxonomy,
                        "type": "direct_reference",
                        "confidence": 0.8
                    }
                ),
                lx.data.Extraction(
                    extraction_class="technical_method",
                    extraction_text="novel methods and compositions",
                    attributes={
                        "innovation_type": "method_and_composition",
                        "purpose": "enhanced performance",
                        "confidence": 0.7
                    }
                )
            ]

        return [lx.data.ExampleData(text=example_text, extractions=extractions)]

    def create_extraction_prompt(self, taxonomy: str) -> str:
        """
        Create extraction prompt for a specific taxonomy

        Args:
            taxonomy: The taxonomy category name

        Returns:
            Formatted prompt string
        """
        return f"""
        Extract text segments from patent documents that are relevant to the taxonomy: "{taxonomy}".

        Guidelines:
        1. Look for text that directly mentions, describes, or relates to {taxonomy}
        2. Include context around relevant mentions (full sentences/paragraphs)
        3. Extract multiple chunks if the taxonomy appears in different sections
        4. Focus on technical descriptions, implementations, and specific details
        5. Avoid generic or irrelevant text
        6. Provide confidence scores based on relevance strength
        7. Extract key technical terms that relate to the taxonomy

        Use exact text for extractions. Do not paraphrase or overlap entities.
        Provide meaningful attributes for each entity to add context.
        """

    def extract_for_taxonomy(self, patent_data: PatentData, taxonomy: str) -> TaxonomyResult:
        """
        Extract relevant text for a specific taxonomy from patent data using LangExtract

        Args:
            patent_data: Patent information
            taxonomy: Taxonomy to extract for

        Returns:
            TaxonomyResult with extracted information
        """
        # Combine patent text sections
        combined_text = f"""
        Title: {patent_data.title}

        Abstract: {patent_data.abstract}

        Claims: {patent_data.claims}

        Description: {patent_data.description}
        """.strip()

        # Create prompt and examples for this taxonomy
        prompt = self.create_extraction_prompt(taxonomy)
        examples = self.create_taxonomy_examples(taxonomy)

        try:
            # Use langextract to perform extraction
            result = lx.extract(
                text_or_documents=combined_text,
                prompt_description=prompt,
                examples=examples,
                model_id=self.model_id,
                api_key=self.api_key,
                max_char_buffer=self.chunk_size,
                extraction_passes=2,  # Multiple passes for better recall
                max_workers=4  # Parallel processing
            )

            # Process the langextract result into our format
            extracted_chunks = []
            confidence_scores = []
            source_spans = []

            if hasattr(result, 'extractions') and result.extractions:
                for extraction in result.extractions:
                    extracted_chunks.append(extraction.extraction_text)

                    # Get confidence from attributes or default
                    confidence = extraction.attributes.get('confidence', 0.7) if extraction.attributes else 0.7
                    confidence_scores.append(float(confidence))

                    # Get source spans if available
                    if hasattr(extraction, 'char_interval') and extraction.char_interval:
                        source_spans.append((extraction.char_interval.start, extraction.char_interval.end))
                    else:
                        # Find approximate position in text
                        start_pos = combined_text.find(extraction.extraction_text)
                        if start_pos >= 0:
                            source_spans.append((start_pos, start_pos + len(extraction.extraction_text)))
                        else:
                            source_spans.append((0, 0))

            return TaxonomyResult(
                taxonomy=taxonomy,
                extracted_chunks=extracted_chunks,
                confidence_scores=confidence_scores,
                source_spans=source_spans,
                context=f"Processed patent {patent_data.patent_number} using LangExtract with {len(extracted_chunks)} extractions"
            )

        except Exception as e:
            print(f"Error processing taxonomy {taxonomy} for patent {patent_data.patent_number}: {str(e)}")
            return TaxonomyResult(
                taxonomy=taxonomy,
                extracted_chunks=[],
                confidence_scores=[],
                source_spans=[],
                context=f"Error: {str(e)}"
            )



    def process_patent_with_taxonomies(self,
                                       patent_number: str,
                                       taxonomies: List[str],
                                       min_confidence: float = None) -> Dict[str, TaxonomyResult]:
        """
        Process a patent for multiple taxonomies using LangExtract

        Args:
            patent_number: Patent number to process
            taxonomies: List of taxonomy categories
            min_confidence: Minimum confidence threshold for results (uses config default if None)

        Returns:
            Dictionary mapping taxonomies to their results
        """
        if min_confidence is None:
            min_confidence = Config.MIN_CONFIDENCE_THRESHOLD

        # Fetch patent data
        patent_data = self.fetch_patent_data(patent_number)

        results = {}

        for taxonomy in taxonomies:
            try:
                result = self.extract_for_taxonomy(patent_data, taxonomy)

                # Filter by confidence threshold
                if min_confidence > 0 and result.confidence_scores:
                    filtered_chunks = []
                    filtered_scores = []
                    filtered_spans = []

                    for i, score in enumerate(result.confidence_scores):
                        if score >= min_confidence:
                            if i < len(result.extracted_chunks):
                                filtered_chunks.append(result.extracted_chunks[i])
                            if i < len(result.source_spans):
                                filtered_spans.append(result.source_spans[i])
                            filtered_scores.append(score)

                    result.extracted_chunks = filtered_chunks
                    result.confidence_scores = filtered_scores
                    result.source_spans = filtered_spans

                results[taxonomy] = result

            except Exception as e:
                print(f"Error processing taxonomy {taxonomy}: {str(e)}")
                results[taxonomy] = TaxonomyResult(
                    taxonomy=taxonomy,
                    extracted_chunks=[],
                    confidence_scores=[],
                    source_spans=[],
                    context=f"Error: {str(e)}"
                )

        return results

    def export_results_to_dataframe(self,
                                    results: Dict[str, Dict[str, TaxonomyResult]]) -> pd.DataFrame:
        """
        Export results to a pandas DataFrame

        Args:
            results: Dictionary of patent_number -> taxonomy -> TaxonomyResult

        Returns:
            DataFrame with structured results
        """
        rows = []

        for patent_number, patent_results in results.items():
            for taxonomy, result in patent_results.items():
                if result.extracted_chunks:
                    # Create a row for each extracted chunk
                    for i, chunk in enumerate(result.extracted_chunks):
                        row = {
                            'patent_number': patent_number,
                            'taxonomy': taxonomy,
                            'extracted_text': chunk,
                            'confidence_score': result.confidence_scores[i] if i < len(
                                result.confidence_scores) else 0.0,
                            'source_span_start': result.source_spans[i][0] if i < len(result.source_spans) else 0,
                            'source_span_end': result.source_spans[i][1] if i < len(result.source_spans) else 0,
                            'context': result.context,
                            'chunk_length': len(chunk)
                        }
                        rows.append(row)
                else:
                    # Create a row indicating no results found
                    row = {
                        'patent_number': patent_number,
                        'taxonomy': taxonomy,
                        'extracted_text': '',
                        'confidence_score': 0.0,
                        'source_span_start': 0,
                        'source_span_end': 0,
                        'context': result.context,
                        'chunk_length': 0
                    }
                    rows.append(row)

        return pd.DataFrame(rows)

    def generate_visualization_report(self,
                                      results: Dict[str, TaxonomyResult],
                                      patent_data: PatentData,
                                      output_path: str = "patent_extraction_report.html"):
        """
        Generate an interactive HTML report showing extraction results

        Args:
            results: Extraction results for a single patent
            patent_data: Patent data
            output_path: Path to save HTML report
        """
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Patent Extraction Report - {patent_data.patent_number}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .taxonomy-section {{ margin: 20px 0; padding: 15px; border: 1px solid #ccc; }}
                .chunk {{ margin: 10px 0; padding: 10px; background-color: #f9f9f9; }}
                .confidence {{ font-weight: bold; color: green; }}
                .low-confidence {{ color: orange; }}
                .no-results {{ color: red; }}
            </style>
        </head>
        <body>
            <h1>Patent Extraction Report</h1>
            <h2>Patent: {patent_data.patent_number}</h2>
            <h3>Title: {patent_data.title}</h3>

            <div class="abstract">
                <h4>Abstract:</h4>
                <p>{patent_data.abstract}</p>
            </div>
        """

        for taxonomy, result in results.items():
            html_content += f"""
            <div class="taxonomy-section">
                <h4>Taxonomy: {taxonomy}</h4>
                <p><strong>Context:</strong> {result.context}</p>
            """

            if result.extracted_chunks:
                for i, chunk in enumerate(result.extracted_chunks):
                    confidence = result.confidence_scores[i] if i < len(result.confidence_scores) else 0.0
                    confidence_class = "confidence" if confidence >= 0.7 else "low-confidence"

                    html_content += f"""
                    <div class="chunk">
                        <p class="{confidence_class}">Confidence: {confidence:.2f}</p>
                        <p><strong>Extracted Text:</strong></p>
                        <p>{chunk}</p>
                    </div>
                    """
            else:
                html_content += '<p class="no-results">No relevant text found for this taxonomy.</p>'

            html_content += "</div>"

        html_content += """
        </body>
        </html>
        """

        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"Visualization report saved to: {output_path}")


def main_example():
    """
    Example usage of the PatentLangExtractProcessor
    """
    # Initialize processor with configuration
    processor = PatentLangExtractProcessor(
        model_id="gemini-2.5-flash",  # Uses Config.GEMINI_API_KEY automatically
        chunk_size=Config.DEFAULT_CHUNK_SIZE,
        overlap_size=Config.DEFAULT_OVERLAP
    )

    # Define taxonomies for analysis
    taxonomies = [
        "Genome Recoding",
        "Synthetic organism/ genomically recoded organisms (GRO)",
        "Non-canonical amino acids (ncAA)",
        "Orthogonal translation system (OTS)"
    ]

    # Process patents
    patent_numbers = ["US11408007B2", "US11649450B2", "US11788111B2"]
    all_results = {}

    # Create output directories
    os.makedirs(Config.OUTPUT_DIR, exist_ok=True)
    os.makedirs(Config.REPORTS_DIR, exist_ok=True)

    for patent_number in patent_numbers:
        try:
            print(f"Processing patent: {patent_number}")
            results = processor.process_patent_with_taxonomies(
                patent_number=patent_number,
                taxonomies=taxonomies
            )
            all_results[patent_number] = results

            # Generate individual report
            patent_data = processor.fetch_patent_data(patent_number)
            report_path = os.path.join(Config.REPORTS_DIR, f"report_{patent_number}.html")
            processor.generate_visualization_report(
                results=results,
                patent_data=patent_data,
                output_path=report_path
            )

            print(f"  - Generated report: {report_path}")

        except Exception as e:
            print(f"Error processing patent {patent_number}: {str(e)}")

    # Export to DataFrame
    df = processor.export_results_to_dataframe(all_results)
    output_path = os.path.join(Config.OUTPUT_DIR, "langextract_patent_results.csv")
    df.to_csv(output_path, index=False)

    print("Processing complete!")
    print(f"Results saved to: {output_path}")
    print(f"Total rows in results: {len(df)}")
    print(f"Reports saved to: {Config.REPORTS_DIR}")


if __name__ == "__main__":
    main_example()